@extends('provider.layout.master')

@section('title', __('provider.notifications'))

@section('content')
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h4 class="card-title">{{__('provider.notifications')}}</h4>
                <div>
                    <button type="button" class="btn btn-sm btn-outline-primary" id="mark-all-read-btn">
                        <i class="feather icon-check"></i> {{__('provider.mark_all_read')}}
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-danger" id="delete-all-btn">
                        <i class="feather icon-trash-2"></i> {{__('provider.delete_all')}}
                    </button>
                </div>
            </div>
            <div class="card-content">
                <div class="card-body">
                    @if($notifications->count() > 0)
                        <div class="list-group">
                            @foreach($notifications as $notification)
                                <div class="list-group-item notification-item {{ $notification->read_at ? '' : 'unread' }}" data-id="{{ $notification->id }}">
                                    <div class="d-flex w-100 justify-content-between align-items-start">
                                        <div class="flex-grow-1 notification-content" style="cursor: pointer;" onclick="window.location.href='{{ route('provider.notifications.click', $notification->id) }}'">
                                            <div class="d-flex align-items-center mb-1">
                                                <div class="avatar bg-light-primary mr-2">
                                                    <div class="avatar-content">
                                                        <i class="feather icon-bell font-medium-3"></i>
                                                    </div>
                                                </div>
                                                <h6 class="mb-0 font-weight-bold">
                                                    {{ $notification->title ?? __('provider.new_notification') }}
                                                </h6>
                                                @if(!$notification->read_at)
                                                    <span class="badge badge-primary ml-2">{{__('provider.new')}}</span>
                                                @endif
                                            </div>
                                            <p class="mb-1 text-muted">
                                                {{ $notification->body ?? $notification->data['message'] ?? __('provider.notification_content') }}
                                            </p>
                                            <small class="text-muted">
                                                <i class="feather icon-clock"></i>
                                                {{ $notification->created_at->diffForHumans() }}
                                            </small>
                                        </div>
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-toggle="dropdown">
                                                <i class="feather icon-more-vertical"></i>
                                            </button>
                                            <div class="dropdown-menu dropdown-menu-right">
                                                @if(!$notification->read_at)
                                                    <a class="dropdown-item mark-read-btn" href="#" data-id="{{ $notification->id }}">
                                                        <i class="feather icon-check"></i> {{__('provider.mark_as_read')}}
                                                    </a>
                                                @endif
                                                <a class="dropdown-item text-danger delete-btn" href="#" data-id="{{ $notification->id }}">
                                                    <i class="feather icon-trash-2"></i> {{__('provider.delete')}}
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>

                        {{-- Pagination --}}
                        <div class="d-flex justify-content-center mt-3">
                            {{ $notifications->links() }}
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="feather icon-bell-off font-large-2 text-muted"></i>
                            <h4 class="mt-2">{{__('provider.no_notifications')}}</h4>
                            <p class="text-muted">{{__('provider.no_notifications_desc')}}</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('js')
<script>
$(document).ready(function() {
    // Mark single notification as read
    $('.mark-read-btn').on('click', function(e) {
        e.preventDefault();
        let notificationId = $(this).data('id');
        let notificationItem = $(`.notification-item[data-id="${notificationId}"]`);
        
        $.ajax({
            url: '{{ route("provider.notifications.mark-read", ":id") }}'.replace(':id', notificationId),
            method: 'POST',
            data: {
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                if (response.success) {
                    notificationItem.removeClass('unread');
                    notificationItem.find('.badge-primary').remove();
                    notificationItem.find('.mark-read-btn').parent().remove();
                    toastr.success(response.message);
                }
            },
            error: function() {
                toastr.error('{{__("provider.error_occurred")}}');
            }
        });
    });

    // Delete single notification
    $('.delete-btn').on('click', function(e) {
        e.preventDefault();
        let notificationId = $(this).data('id');
        let notificationItem = $(`.notification-item[data-id="${notificationId}"]`);
        
        Swal.fire({
            title: '{{__("provider.are_you_sure")}}',
            text: '{{__("provider.delete_notification_confirm")}}',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: '{{__("provider.yes_delete")}}',
            cancelButtonText: '{{__("provider.cancel")}}'
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: '{{ route("provider.notifications.delete", ":id") }}'.replace(':id', notificationId),
                    method: 'DELETE',
                    data: {
                        _token: '{{ csrf_token() }}'
                    },
                    success: function(response) {
                        if (response.success) {
                            notificationItem.fadeOut(300, function() {
                                $(this).remove();
                            });
                            toastr.success(response.message);
                        }
                    },
                    error: function() {
                        toastr.error('{{__("provider.error_occurred")}}');
                    }
                });
            }
        });
    });

    // Mark all notifications as read
    $('#mark-all-read-btn').on('click', function(e) {
        e.preventDefault();
        
        $.ajax({
            url: '{{ route("provider.notifications.mark-all-read") }}',
            method: 'POST',
            data: {
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                if (response.success) {
                    $('.notification-item').removeClass('unread');
                    $('.badge-primary').remove();
                    $('.mark-read-btn').parent().remove();
                    toastr.success(response.message);
                }
            },
            error: function() {
                toastr.error('{{__("provider.error_occurred")}}');
            }
        });
    });

    // Delete all notifications
    $('#delete-all-btn').on('click', function(e) {
        e.preventDefault();
        
        Swal.fire({
            title: '{{__("provider.are_you_sure")}}',
            text: '{{__("provider.delete_all_notifications_confirm")}}',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: '{{__("provider.yes_delete_all")}}',
            cancelButtonText: '{{__("provider.cancel")}}'
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: '{{ route("provider.notifications.delete-all") }}',
                    method: 'DELETE',
                    data: {
                        _token: '{{ csrf_token() }}'
                    },
                    success: function(response) {
                        if (response.success) {
                            location.reload();
                        }
                    },
                    error: function() {
                        toastr.error('{{__("provider.error_occurred")}}');
                    }
                });
            }
        });
    });
});
</script>

<style>
.notification-item.unread {
    background-color: #f8f9fa;
    border-left: 4px solid #7367f0;
}

.notification-item {
    border: 1px solid #e9ecef;
    border-radius: 0.5rem;
    margin-bottom: 0.5rem;
    padding: 1rem;
    transition: all 0.3s ease;
}

.notification-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.bg-light-primary {
    background-color: rgba(115, 103, 240, 0.12);
    color: #7367f0;
}
</style>
@endsection
