<?php

namespace App\Http\Controllers\Provider;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class NotificationController extends Controller
{
    /**
     * Display a listing of notifications.
     */
    public function index()
    {
        $notifications = Auth::guard('provider')->user()->notifications()->paginate(15);
        
        return view('provider.notifications.index', compact('notifications'));
    }

    /**
     * Get notifications for AJAX dropdown.
     */
    public function getNotifications()
    {
        $user = Auth::guard('provider')->user();
        $notifications = $user->unreadNotifications()->limit(5)->get();
        $unreadCount = $user->unreadNotifications()->count();
        
        return response()->json([
            'notifications' => $notifications->map(function ($notification) {
                return [
                    'id' => $notification->id,
                    'title' => $notification->title ?? 'إشعار جديد',
                    'body' => $notification->body ?? $notification->data['message'] ?? 'لديك إشعار جديد',
                    'created_at' => $notification->created_at->diffFor<PERSON>umans(),
                    'read_at' => $notification->read_at,
                ];
            }),
            'unread_count' => $unreadCount,
        ]);
    }

    /**
     * Mark all notifications as read.
     */
    public function markAllAsRead()
    {
        Auth::guard('provider')->user()->unreadNotifications->markAsRead();
        
        return response()->json([
            'success' => true,
            'message' => __('provider.notifications_marked_read')
        ]);
    }

    /**
     * Mark a specific notification as read.
     */
    public function markAsRead($id)
    {
        $notification = Auth::guard('provider')->user()->notifications()->find($id);
        
        if ($notification) {
            $notification->markAsRead();
            
            return response()->json([
                'success' => true,
                'message' => __('provider.notification_marked_read')
            ]);
        }
        
        return response()->json([
            'success' => false,
            'message' => __('provider.notification_not_found')
        ], 404);
    }

    /**
     * Delete a specific notification.
     */
    public function delete($id)
    {
        $notification = Auth::guard('provider')->user()->notifications()->find($id);
        
        if ($notification) {
            $notification->delete();
            
            return response()->json([
                'success' => true,
                'message' => __('provider.notification_deleted')
            ]);
        }
        
        return response()->json([
            'success' => false,
            'message' => __('provider.notification_not_found')
        ], 404);
    }

    /**
     * Delete all notifications.
     */
    public function deleteAll()
    {
        Auth::guard('provider')->user()->notifications()->delete();
        
        return response()->json([
            'success' => true,
            'message' => __('provider.all_notifications_deleted')
        ]);
    }
}
