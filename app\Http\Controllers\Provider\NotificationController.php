<?php

namespace App\Http\Controllers\Provider;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class NotificationController extends Controller
{
    /**
     * Display a listing of notifications.
     */
    public function index()
    {
        $notifications = Auth::guard('provider')->user()->notifications()->paginate(15);
        
        return view('provider.notifications.index', compact('notifications'));
    }

    /**
     * Get notifications for AJAX dropdown.
     */
    public function getNotifications()
    {
        $user = Auth::guard('provider')->user();
        $notifications = $user->unreadNotifications()->limit(5)->get();
        $unreadCount = $user->unreadNotifications()->count();

        return response()->json([
            'notifications' => $notifications->map(function ($notification) {
                $data = $notification->data;
                return [
                    'id' => $notification->id,
                    'title' => $data['title'][app()->getLocale()] ?? $data['title']['ar'] ?? __('provider.new_notification'),
                    'body' => $data['body'][app()->getLocale()] ?? $data['body']['ar'] ?? $data['message'] ?? __('provider.notification_content'),
                    'created_at' => $notification->created_at->diffForHumans(),
                    'read_at' => $notification->read_at,
                    'type' => $data['type'] ?? 'general',
                ];
            }),
            'unread_count' => $unreadCount,
        ]);
    }

    /**
     * Mark all notifications as read.
     */
    public function markAllAsRead()
    {
        Auth::guard('provider')->user()->unreadNotifications->markAsRead();
        
        return response()->json([
            'success' => true,
            'message' => __('provider.notifications_marked_read')
        ]);
    }

    /**
     * Mark a specific notification as read.
     */
    public function markAsRead($id)
    {
        $notification = Auth::guard('provider')->user()->notifications()->find($id);

        if ($notification) {
            $notification->markAsRead();

            return response()->json([
                'success' => true,
                'message' => __('provider.notification_marked_read')
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => __('provider.notification_not_found')
        ], 404);
    }

    /**
     * Handle notification click and redirect to appropriate page.
     */
    public function handleClick($id)
    {
        $notification = Auth::guard('provider')->user()->notifications()->find($id);

        if (!$notification) {
            return redirect()->route('provider.notifications.index')
                ->with('error', __('provider.notification_not_found'));
        }

        // Mark as read
        $notification->markAsRead();

        // Handle different notification types
        $data = $notification->data;
        $type = $data['type'] ?? null;

        switch ($type) {
            case 'new_message':
                // Redirect to chat with specific conversation
                if (isset($data['conversation_id'])) {
                    return redirect()->route('provider.chat.index')
                        ->with('conversation_id', $data['conversation_id'])
                        ->with('success', __('provider.notification_opened'));
                }
                break;

            case 'new_order':
                // Redirect to orders page
                if (isset($data['order_id'])) {
                    return redirect()->route('provider.orders.show', $data['order_id'])
                        ->with('success', __('provider.notification_opened'));
                }
                break;

            default:
                // Default redirect to notifications page
                return redirect()->route('provider.notifications.index')
                    ->with('success', __('provider.notification_opened'));
        }

        // Fallback redirect
        return redirect()->route('provider.dashboard')
            ->with('success', __('provider.notification_opened'));
    }

    /**
     * Delete a specific notification.
     */
    public function delete($id)
    {
        $notification = Auth::guard('provider')->user()->notifications()->find($id);
        
        if ($notification) {
            $notification->delete();
            
            return response()->json([
                'success' => true,
                'message' => __('provider.notification_deleted')
            ]);
        }
        
        return response()->json([
            'success' => false,
            'message' => __('provider.notification_not_found')
        ], 404);
    }

    /**
     * Delete all notifications.
     */
    public function deleteAll()
    {
        Auth::guard('provider')->user()->notifications()->delete();
        
        return response()->json([
            'success' => true,
            'message' => __('provider.all_notifications_deleted')
        ]);
    }
}
