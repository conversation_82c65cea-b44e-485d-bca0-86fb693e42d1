<!DOCTYPE html>
<html class="loading" lang="en" data-textdirection="rtl">
<!-- BEGIN: Head-->

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0">
    <meta name="description" content="Vuexy admin is super flexible, powerful, clean &amp; modern responsive bootstrap 4 admin template with unlimited possibilities.">
    <meta name="keywords" content="admin template, Vuexy admin template, dashboard template, flat admin template, responsive admin template, web app">
    <meta name="author" content="PIXINVENT">
    <title><?php echo $__env->yieldContent('title',isset(\Request::route()->getAction()['title']) ? __('admin.'.\Request::route()->getAction()['title']) : ''); ?></title>
    <?php
    $secureFavicon = str_replace('http://', 'https://', $settings['fav_icon']);
?>

<link rel="apple-touch-icon" href="<?php echo e($secureFavicon); ?>">
<link rel="shortcut icon" type="image/x-icon" href="<?php echo e($secureFavicon); ?>">    <link href="https://fonts.googleapis.com/css?family=Montserrat:300,400,500,600" rel="stylesheet">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('admin/app-assets/vendors/css/vendors.min.css')); ?>">
    <link rel="stylesheet" href="<?php echo e(asset('admin/app-assets/css/bootstrap.css')); ?>">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/switchery/0.8.2/switchery.min.css" integrity="sha512-uyGg6dZr3cE1PxtKOCGqKGTiZybe5iSq3LsqOolABqAWlIRLo/HKyrMMD8drX+gls3twJdpYX0gDKEdtf2dpmw==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>


    <?php if(in_array(lang(),json_decode($settings['rtl_locales']))): ?>
        <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/app-assets/vendors/css/vendors-rtl.min.css')); ?>">
        <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/app-assets/css-rtl/bootstrap.css')); ?>">
        <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/app-assets/css-rtl/bootstrap-extended.css')); ?>">
        <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/app-assets/css-rtl/colors.css')); ?>">
        <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/app-assets/css-rtl/components.css')); ?>">
        <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/app-assets/css-rtl/themes/dark-layout.css')); ?>">
        <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/app-assets/css-rtl/themes/semi-dark-layout.css')); ?>">
        <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/app-assets/css-rtl/core/menu/menu-types/vertical-menu.css')); ?>">
        <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/app-assets/css-rtl/core/colors/palette-gradient.css')); ?>">
        <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/app-assets/css-rtl/pages/dashboard-ecommerce.css')); ?>">
        <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/app-assets/css-rtl/pages/card-analytics.css')); ?>">
        <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/app-assets/css-rtl/custom-rtl.css')); ?>">
        <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/assets/css/style-rtl.css')); ?>">
    <?php else: ?>
        <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/app-assets/vendors/css/vendors.min.css')); ?>">
        <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/app-assets/css/bootstrap.css')); ?>">
        <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/app-assets/css/bootstrap-extended.css')); ?>">
        <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/app-assets/css/colors.css')); ?>">
        <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/app-assets/css/components.css')); ?>">
        <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/app-assets/css/themes/dark-layout.css')); ?>">
        <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/app-assets/css/themes/semi-dark-layout.css')); ?>">
        <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/app-assets/css/core/menu/menu-types/vertical-menu.css')); ?>">
        <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/app-assets/css/core/colors/palette-gradient.css')); ?>">
        <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/app-assets/css/pages/dashboard-ecommerce.css')); ?>">
        <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/app-assets/css/pages/card-analytics.css')); ?>">
        <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/app-assets/css/custom-rtl.css')); ?>">
        <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/assets/css/style_en.css')); ?>">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">

    <?php endif; ?>
    
    <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/assets/css/style.css')); ?>">

    <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/assets/css/flatpickr.css')); ?>">

    <link rel="stylesheet" type="text/css" href="https://code.jquery.com/ui/1.12.0/themes/smoothness/jquery-ui.css">


    <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/app-assets/vendors/css/extensions/toastr.css')); ?>">
    <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/app-assets/css-rtl/plugins/extensions/toastr.css')); ?>">

    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <?php echo $__env->yieldContent('css'); ?>

    
    <style>
        <?php if(app()->getLocale() == 'ar'): ?>
        /* RTL Navbar Layout - Only for Arabic */
        .navbar-collapse.d-flex {
            flex-direction: row-reverse !important;
        }

        .breadcrumb-wrapper.order-2 {
            margin-left: auto !important;
            margin-right: 0 !important;
        }

        .resp-wrap-icon.order-1 {
            margin-right: auto !important;
            margin-left: 0 !important;
        }

        /* Breadcrumb RTL styling */
        .breadcrumb-text {
            text-align: right;
            direction: rtl;
        }

        .breadcrumb-text .hint-slash {
            margin: 0 5px;
        }

        /* Dropdown menu RTL */
        .dropdown-menu-right {
            right: auto !important;
            left: 0 !important;
        }

        /* Language dropdown RTL */
        .dropdown-language .dropdown-menu {
            right: auto !important;
            left: 0 !important;
        }
        <?php else: ?>
        /* LTR Navbar Layout - Only for English */
        .breadcrumb-wrapper {
            margin-right: auto !important;
            margin-left: 0 !important;
        }

        .breadcrumb-text {
            text-align: left;
            direction: ltr;
        }

        .breadcrumb-text .hint-slash {
            margin: 0 5px;
        }
        <?php endif; ?>

        /* Language Toggle Switch Styles */
        .language-switch-wrapper {
            padding: 0.5rem;
        }

        .language-label {
            font-size: 14px;
            font-weight: 600;
            color: #6e6b7b;
            transition: color 0.3s ease;
        }

        .language-label.active {
            color: #7367f0;
        }

        .language-toggle-switch {
            position: relative;
            display: inline-block;
        }

        .language-toggle-switch input[type="checkbox"] {
            display: none;
        }

        .toggle-label {
            display: block;
            width: 40px;
            height: 20px;
            background-color: #ddd;
            border-radius: 20px;
            cursor: pointer;
            transition: background-color 0.3s ease;
            position: relative;
        }

        .toggle-slider {
            position: absolute;
            top: 2px;
            left: 2px;
            width: 16px;
            height: 16px;
            background-color: white;
            border-radius: 50%;
            transition: transform 0.3s ease;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .language-toggle-switch input[type="checkbox"]:checked + .toggle-label {
            background-color: #7367f0;
        }

        .language-toggle-switch input[type="checkbox"]:checked + .toggle-label .toggle-slider {
            transform: translateX(20px);
        }

        .language-toggle-switch:hover .toggle-label {
            background-color: #bbb;
        }

        .language-toggle-switch input[type="checkbox"]:checked:hover + .toggle-label {
            background-color: #5a52d5;
        }

        /* RTL adjustments for language toggle */
        <?php if(app()->getLocale() == 'ar'): ?>
        .language-switch-wrapper {
            flex-direction: row-reverse;
        }
        <?php endif; ?>

        /* Language Selector Styles */
        #languageSelector {
            display: flex !important;
            align-items: center;
            justify-content: center;
        }

        #languageSelector .current-language {
            font-size: 12px;
            font-weight: 600;
            margin-left: 4px;
            line-height: 1;
        }

        /* RTL adjustments for language selector */
        <?php if(app()->getLocale() == 'ar'): ?>
        #languageSelector .current-language {
            margin-left: 0;
            margin-right: 4px;
        }
        <?php endif; ?>

        /* Notifications Dropdown Styles */
        .dropdown-notification .dropdown-menu {
            width: 350px;
            max-height: 400px;
            overflow-y: auto;
        }

        .dropdown-notification .dropdown-menu-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            padding: 1rem;
        }

        .dropdown-notification .dropdown-menu-footer {
            background-color: #f8f9fa;
            border-top: 1px solid #e9ecef;
            padding: 0.75rem 1rem;
        }

        .dropdown-notification .media-list {
            max-height: 250px;
            overflow-y: auto;
        }

        .dropdown-notification .media-list .media {
            padding: 0.75rem 1rem;
            border-bottom: 1px solid #f1f1f1;
            transition: background-color 0.3s ease;
        }

        .dropdown-notification .media-list .media:hover {
            background-color: #f8f9fa;
        }

        .dropdown-notification .media-list .media:last-child {
            border-bottom: none;
        }

        .notification-count {
            font-size: 0.75rem;
            min-width: 18px;
            height: 18px;
            line-height: 18px;
            text-align: center;
            animation: pulse 2s infinite;
        }

        .notification-count.badge-up {
            top: -8px;
            right: -8px;
        }

        .scrollable-container {
            scrollbar-width: thin;
            scrollbar-color: #ccc transparent;
        }

        .scrollable-container::-webkit-scrollbar {
            width: 6px;
        }

        .scrollable-container::-webkit-scrollbar-track {
            background: transparent;
        }

        .scrollable-container::-webkit-scrollbar-thumb {
            background-color: #ccc;
            border-radius: 3px;
        }

        .scrollable-container::-webkit-scrollbar-thumb:hover {
            background-color: #999;
        }

        .spin {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
    </style>
</head>
<!-- END: Head-->

<!-- BEGIN: Body-->


<body style="font-family: 'Cairo', sans-serif !important;" id="content_body" class="position-relative vertical-layout vertical-menu-modern 2-columns  navbar-floating footer-static" data-open="click" data-menu="vertical-menu-modern" data-col="2-columns">
    



        <?php /**PATH D:\Workstation\Taswk\elhafla\resources\views/provider/layout/partial/header.blade.php ENDPATH**/ ?>