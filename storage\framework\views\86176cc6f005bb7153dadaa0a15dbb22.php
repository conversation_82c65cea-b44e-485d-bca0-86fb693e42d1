

<?php $__env->startSection('css'); ?>
<style>
.chat-container {
    height: 75vh;
    display: flex;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
}

.conversations-list {
    width: 30%;
    border-right: 1px solid #e0e0e0;
    background: #f8f9fa;
    overflow-y: auto;
    max-height: 75vh;
}

.conversations-list::-webkit-scrollbar {
    width: 6px;
}

.conversations-list::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.conversations-list::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.conversations-list::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

.chat-area {
    width: 70%;
    display: flex;
    flex-direction: column;
    height: 75vh;
    position: relative;
}

.conversation-item {
    padding: 15px;
    border-bottom: 1px solid #e0e0e0;
    cursor: pointer;
    transition: background-color 0.2s;
}

.conversation-item:hover {
    background-color: #e9ecef;
}

.conversation-item.active {
    background-color: #007bff;
    color: white;
}

.messages-container {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    background: white;
    height: calc(75vh - 160px);
    min-height: 300px;
}

.messages-container::-webkit-scrollbar {
    width: 8px;
}

.messages-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.messages-container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.messages-container::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

.message {
    margin-bottom: 15px;
    display: flex;
}

.message.sent {
    justify-content: flex-end;
}

.message.received {
    justify-content: flex-start;
}

.message-bubble {
    max-width: 70%;
    padding: 10px 15px;
    border-radius: 18px;
    word-wrap: break-word;
}

.message.sent .message-bubble {
    background-color: #007bff;
    color: white;
}

.message.received .message-bubble {
    background-color: #e9ecef;
    color: #333;
}

.message-input-area {
    padding: 20px;
    border-top: 1px solid #e0e0e0;
    background: #f8f9fa;
    position: sticky;
    bottom: 0;
    width: 100%;
    flex-shrink: 0;
    z-index: 10;
    margin-top: auto;
    min-height: 80px;
    box-sizing: border-box;
}

/* Ensure the input area is always visible */
.chat-area .message-input-area {
    display: block !important;
    visibility: visible !important;
}

/* Force the chat content to use proper flex layout */
#chat-content {
    min-height: 75vh;
    max-height: 75vh;
}

/* Ensure proper stacking order */
#chat-content > .message-input-area {
    order: 999;
    position: relative;
    bottom: 0;
}

.unread-badge {
    background-color: #dc3545;
    color: white;
    border-radius: 50%;
    padding: 2px 6px;
    font-size: 0.8em;
    margin-left: 10px;
}

.no-conversation {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #6c757d;
    font-size: 1.1em;
}

#chat-content {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.chat-header {
    flex-shrink: 0;
}

.typing-indicator {
    flex-shrink: 0;
    padding: 10px 20px;
    font-style: italic;
    color: #6c757d;
    font-size: 0.9em;
}
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="content-header row">
    <div class="content-header-left col-md-9 col-12 mb-2">
        <div class="row breadcrumbs-top">
            <div class="col-12">
                <h2 class="content-header-title float-left mb-0"><?php echo e(__('provider.chat')); ?></h2>
                <div class="breadcrumb-wrapper col-12">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="<?php echo e(route('provider.dashboard')); ?>"><?php echo e(__('provider.dashboard')); ?></a></li>
                        <li class="breadcrumb-item active"><?php echo e(__('provider.chat')); ?></li>
                    </ol>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="content-body">
    <section class="chat-section">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body p-0">
                        <div class="chat-container">
                            <!-- Conversations List -->
                            <div class="conversations-list">
                                <div class="p-3 border-bottom">
                                    <h5 class="mb-0"><?php echo e(__('provider.conversations')); ?></h5>
                                </div>
                                <div id="conversations-container">
                                    <div class="text-center p-4">
                                        <div class="spinner-border" role="status">
                                            <span class="sr-only"><?php echo e(__('provider.loading')); ?></span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Chat Area -->
                            <div class="chat-area">
                                <div id="no-conversation" class="no-conversation">
                                    <div class="text-center">
                                        <i class="feather icon-message-circle font-large-2 mb-2"></i>
                                        <p><?php echo e(__('provider.select_conversation')); ?></p>
                                    </div>
                                </div>

                                <div id="chat-content" style="display: none;">
                                    <!-- Chat Header -->
                                    <div class="chat-header p-3 border-bottom">
                                        <div class="d-flex align-items-center justify-content-between">
                                            <div class="d-flex align-items-center">
                                                <img id="chat-user-avatar" src="" alt="User" class="rounded-circle mr-2" width="40" height="40">
                                                <div>
                                                    <h6 id="chat-user-name" class="mb-0"></h6>
                                                    <small id="chat-user-status" class="text-muted"></small>
                                                </div>
                                            </div>
                                            <div class="connection-status">
                                                <span id="connection-indicator" class="badge badge-success"><?php echo e(__('provider.connected')); ?></span>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Messages Container -->
                                    <div class="messages-container" id="messages-container">
                                        <!-- Messages will be loaded here -->
                                    </div>

                                    <!-- Typing Indicator -->
                                    <div id="typing-indicator" class="typing-indicator" style="display: none;">
                                        <span id="typing-user"></span> <?php echo e(__('provider.is_typing')); ?>...
                                    </div>

                                    <!-- Message Input -->
                                    <div class="message-input-area">
                                        <form id="message-form">
                                            <div class="input-group">
                                                <input type="text" id="message-input" class="form-control" placeholder="<?php echo e(__('provider.type_message')); ?>" required>
                                                <div class="input-group-append">
                                                    <button type="submit" class="btn btn-primary">
                                                        <i class="feather icon-send"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('js'); ?>
<script src="https://js.pusher.com/8.2.0/pusher.min.js"></script>
<script>
// Chat functionality will be added here
let currentConversationId = null;
let pusher = null;
let channel = null;
let typingTimer = null;

$(document).ready(function() {
    initializeChat();
    loadConversations();

    // Check if we need to open a specific conversation from notification
    <?php if(session('conversation_id')): ?>
        const targetConversationId = <?php echo e(session('conversation_id')); ?>;
        setTimeout(function() {
            openConversationFromNotification(targetConversationId);
        }, 1000); // Wait for conversations to load
    <?php endif; ?>

    // Debug: Check if message input area is visible
    setInterval(() => {
        const inputArea = $('.message-input-area');
        if (inputArea.length > 0 && currentConversationId) {
            if (!inputArea.is(':visible')) {
                console.log('Message input area is hidden, showing it...');
                inputArea.show().css('display', 'block');
            }
        }
    }, 2000);
});

function initializeChat() {
    // Initialize Pusher
    pusher = new Pusher('d89883cbb9ecf64d0429', {
        cluster: 'eu',
        encrypted: true,
        authEndpoint: '/broadcasting/auth',
        auth: {
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
                'Accept': 'application/json'
            }
        }
    });

    // Subscribe to user's private channel for global notifications
    const userId = <?php echo e(auth()->guard('provider')->id()); ?>;
    const userChannel = pusher.subscribe(`private-user.${userId}`);

    userChannel.bind('new.message.notification', function(data) {
        console.log('New message notification:', data);

        // Show browser notification if supported
        if (Notification.permission === 'granted') {
            new Notification(data.notification_text, {
                body: data.message,
                icon: data.sender.image,
                tag: `conversation-${data.conversation_id}`
            });
        }

        // Update conversation list
        updateConversationInList(data.conversation_id, data);

        // Play notification sound
        playNotificationSound();
    });

    // Request notification permission
    if (Notification.permission === 'default') {
        Notification.requestPermission();
    }

    // Monitor connection state
    pusher.connection.bind('connected', function() {
        console.log('Pusher connected');
        $('#connection-indicator').removeClass('badge-danger badge-warning').addClass('badge-success').text('<?php echo e(__("provider.connected")); ?>');
    });

    pusher.connection.bind('disconnected', function() {
        console.log('Pusher disconnected');
        $('#connection-indicator').removeClass('badge-success badge-warning').addClass('badge-danger').text('<?php echo e(__("provider.disconnected")); ?>');
    });

    pusher.connection.bind('connecting', function() {
        console.log('Pusher connecting');
        $('#connection-indicator').removeClass('badge-success badge-danger').addClass('badge-warning').text('<?php echo e(__("provider.connecting")); ?>');
    });

    pusher.connection.bind('error', function(error) {
        console.error('Pusher connection error:', error);
        $('#connection-indicator').removeClass('badge-success badge-warning').addClass('badge-danger').text('Connection Error');
    });

    // Handle authentication errors
    pusher.bind('pusher:error', function(error) {
        console.error('Pusher error:', error);
        if (error.code === 4004) {
            alert('Authentication failed. Please refresh the page and try again.');
        }
    });
}

function loadConversations() {
    $.get('<?php echo e(route("provider.chat.conversations")); ?>')
        .done(function(response) {
            if (response.status) {
                displayConversations(response.conversations);
            }
        })
        .fail(function() {
            $('#conversations-container').html('<div class="text-center p-4 text-danger"><?php echo e(__("provider.error_loading_conversations")); ?></div>');
        });
}

function displayConversations(conversations) {
    let html = '';
    
    if (conversations.length === 0) {
        html = '<div class="text-center p-4 text-muted"><?php echo e(__("provider.no_conversations")); ?></div>';
    } else {
        conversations.forEach(function(conversation) {
            const unreadBadge = conversation.unread_messages_count > 0 
                ? `<span class="unread-badge">${conversation.unread_messages_count}</span>` 
                : '';
            
            html += `
                <div class="conversation-item" data-conversation-id="${conversation.id}" onclick="selectConversation(${conversation.id})">
                    <div class="d-flex align-items-center">
                        <img src="${conversation.other_user.image}" alt="${conversation.other_user.name}" class="rounded-circle mr-2" width="40" height="40">
                        <div class="flex-grow-1">
                            <h6 class="mb-0">${conversation.other_user.name}</h6>
                            <small class="text-muted">${conversation.other_user.type}</small>
                        </div>
                        ${unreadBadge}
                    </div>
                </div>
            `;
        });
    }
    
    $('#conversations-container').html(html);
}

function openConversationFromNotification(conversationId) {
    // Find and click the conversation item
    const conversationItem = $(`.conversation-item[data-conversation-id="${conversationId}"]`);
    if (conversationItem.length > 0) {
        conversationItem.click();
        // Scroll to the conversation item
        conversationItem[0].scrollIntoView({ behavior: 'smooth', block: 'center' });
        // Highlight the conversation briefly
        conversationItem.addClass('bg-light');
        setTimeout(function() {
            conversationItem.removeClass('bg-light');
        }, 2000);

        // Update notification count since we opened from notification
        setTimeout(function() {
            updateNotificationCountFromServerFromServer();
        }, 2000);
    } else {
        // If conversation not found, reload conversations and try again
        loadConversations();
        setTimeout(function() {
            openConversationFromNotification(conversationId);
        }, 1000);
    }
}

function selectConversation(conversationId) {
    currentConversationId = conversationId;
    
    // Update UI
    $('.conversation-item').removeClass('active');
    $(`.conversation-item[data-conversation-id="${conversationId}"]`).addClass('active');
    
    // Load messages
    loadMessages(conversationId);
    
    // Subscribe to conversation channel
    subscribeToConversation(conversationId);
    
    // Show chat content
    $('#no-conversation').hide();
    $('#chat-content').show();

    // Ensure message input area is visible
    $('.message-input-area').show();

    // Force layout recalculation
    setTimeout(() => {
        $('.message-input-area').css('display', 'block');
        scrollToBottom();
    }, 100);

    // Update notification count after a short delay to allow server processing
    setTimeout(function() {
        updateNotificationCountFromServerFromServer();
    }, 1500);
}

function loadMessages(conversationId) {
    $.get(`<?php echo e(route("provider.chat.messages", ":id")); ?>`.replace(':id', conversationId))
        .done(function(response) {
            if (response.status) {
                displayMessages(response.messages.data);
                updateChatHeader(response.conversation.other_user);
            }
        })
        .fail(function() {
            console.error('Failed to load messages');
        });
}

function displayMessages(messages) {
    let html = '';
    const currentUserId = <?php echo e(auth()->guard('provider')->id()); ?>;

    // Check if messages is an array
    if (!Array.isArray(messages)) {
        console.error('Messages is not an array:', messages);
        $('#messages-container').html('<div class="text-center p-4 text-muted">No messages to display</div>');
        return;
    }

    messages.forEach(function(message) {
        const isSent = message.sender.id === currentUserId;
        const messageClass = isSent ? 'sent' : 'received';

        // Check if message has image
        const hasImage = message.first_media_url;
        const imageHtml = hasImage ?
            `<div class="message-image-container">
                <img src="${message.first_media_url}" class="message-image" onclick="openImageModal('${message.first_media_url}')" style="max-width: 200px; max-height: 200px; border-radius: 8px; margin-top: 5px; cursor: pointer;">
             </div>` : '';

        html += `
            <div class="message ${messageClass}" data-message-id="${message.id}">
                <div class="message-bubble">
                    ${message.message || ''}
                    ${imageHtml}
                    <div class="message-time text-muted" style="font-size: 0.8em; margin-top: 5px;">
                        ${formatMessageTime(message.created_at)}
                        ${isSent ? '<span class="read-status ml-1">' + (message.is_read ? '✓✓' : '✓') + '</span>' : ''}
                    </div>
                </div>
            </div>
        `;
    });

    $('#messages-container').html(html);
    scrollToBottom();
}

function updateChatHeader(user) {
    $('#chat-user-avatar').attr('src', user.image);
    $('#chat-user-name').text(user.name);
    $('#chat-user-status').text(user.type);
}

function subscribeToConversation(conversationId) {
    if (channel) {
        pusher.unsubscribe(channel.name);
    }

    channel = pusher.subscribe(`private-chat.${conversationId}`);

    // Listen for new messages
    channel.bind('message.sent', function(data) {
        console.log('New message received:', data);
        addNewMessage(data.message);
        updateConversationInList(conversationId, data.message);
    });

    // Listen for message read status
    channel.bind('message.read', function(data) {
        console.log('Messages marked as read:', data);
        markMessagesAsRead(data.message_ids);
    });

    // Listen for typing indicators
    channel.bind('user.typing', function(data) {
        console.log('User typing:', data);
        const currentUserId = <?php echo e(auth()->guard('provider')->id()); ?>;
        if (data.user.id !== currentUserId) {
            showTypingIndicator(data.user.name, data.is_typing);
        }
    });

    // Handle connection errors
    channel.bind('pusher:subscription_error', function(status) {
        console.error('Pusher subscription error:', status);
    });

    channel.bind('pusher:subscription_succeeded', function() {
        console.log('Successfully subscribed to chat channel:', conversationId);
    });
}

function addNewMessage(message) {
    const currentUserId = <?php echo e(auth()->guard('provider')->id()); ?>;
    const isSent = message.sender.id === currentUserId;
    const messageClass = isSent ? 'sent' : 'received';

    // Check if message already exists to avoid duplicates
    if ($(`[data-message-id="${message.id}"]`).length > 0) {
        return;
    }

    // Check if message has image
    const hasImage = message.first_media_url;
    const imageHtml = hasImage ?
        `<div class="message-image-container">
            <img src="${message.first_media_url}" class="message-image" onclick="openImageModal('${message.first_media_url}')" style="max-width: 200px; max-height: 200px; border-radius: 8px; margin-top: 5px; cursor: pointer;">
         </div>` : '';

    const messageHtml = `
        <div class="message ${messageClass}" data-message-id="${message.id}">
            <div class="message-bubble">
                ${message.message || ''}
                ${imageHtml}
                <div class="message-time text-muted" style="font-size: 0.8em; margin-top: 5px;">
                    ${formatMessageTime(message.created_at)}
                    ${isSent ? '<span class="read-status ml-1">✓</span>' : ''}
                </div>
            </div>
        </div>
    `;

    $('#messages-container').append(messageHtml);
    scrollToBottom();

    // If it's a received message, mark it as read
    if (!isSent && currentConversationId) {
        setTimeout(() => {
            markConversationAsRead(currentConversationId);
        }, 1000);
    }
}

function updateConversationInList(conversationId, lastMessage) {
    // Update the conversation item in the list with the latest message
    const conversationItem = $(`.conversation-item[data-conversation-id="${conversationId}"]`);
    if (conversationItem.length > 0) {
        // Move conversation to top of list
        conversationItem.prependTo('#conversations-container');

        // Update unread count if it's not the current conversation
        if (currentConversationId !== conversationId) {
            const unreadBadge = conversationItem.find('.unread-badge');
            if (unreadBadge.length > 0) {
                const currentCount = parseInt(unreadBadge.text()) || 0;
                unreadBadge.text(currentCount + 1);
            } else {
                conversationItem.find('.flex-grow-1').append('<span class="unread-badge">1</span>');
            }
        }
    } else {
        // Reload conversations if new conversation
        loadConversations();
    }
}

function markMessagesAsRead(messageIds) {
    // Update read status indicators for sent messages
    messageIds.forEach(messageId => {
        const messageElement = $(`[data-message-id="${messageId}"]`);
        if (messageElement.hasClass('sent')) {
            messageElement.find('.read-status').html('✓✓').addClass('text-primary');
        }
    });
}

function markConversationAsRead(conversationId) {
    $.post(`<?php echo e(route("provider.chat.messages", ":id")); ?>`.replace(':id', conversationId) + '/mark-read', {
        _token: '<?php echo e(csrf_token()); ?>'
    })
    .done(function(response) {
        if (response.status) {
            // Remove unread badge from conversation
            $(`.conversation-item[data-conversation-id="${conversationId}"] .unread-badge`).remove();
        }
    });
}

function showTypingIndicator(userName, isTyping) {
    if (isTyping) {
        $('#typing-user').text(userName);
        $('#typing-indicator').show();
    } else {
        $('#typing-indicator').hide();
    }
}

function scrollToBottom() {
    const container = $('#messages-container');
    container.scrollTop(container[0].scrollHeight);
}

function formatMessageTime(timestamp) {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now - date) / (1000 * 60 * 60);

    if (diffInHours < 24) {
        return date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
    } else if (diffInHours < 48) {
        return 'Yesterday ' + date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
    } else {
        return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
    }
}

function openImageModal(imageUrl) {
    // Create modal if it doesn't exist
    if ($('#imageModal').length === 0) {
        $('body').append(`
            <div id="imageModal" style="display: none; position: fixed; z-index: 9999; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.8);">
                <span style="position: absolute; top: 15px; right: 35px; color: #f1f1f1; font-size: 40px; font-weight: bold; cursor: pointer;" onclick="$('#imageModal').hide()">&times;</span>
                <img id="modalImage" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); max-width: 90%; max-height: 90%;">
            </div>
        `);
    }

    $('#modalImage').attr('src', imageUrl);
    $('#imageModal').show();

    // Close modal when clicking outside the image
    $('#imageModal').off('click').on('click', function(e) {
        if (e.target === this) {
            $(this).hide();
        }
    });
}

function playNotificationSound() {
    // Create a simple notification sound
    const audioContext = new (window.AudioContext || window.webkitAudioContext)();
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);

    oscillator.frequency.value = 800;
    oscillator.type = 'sine';

    gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.5);

    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + 0.5);
}

function formatMessageTime(timestamp) {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now - date) / (1000 * 60 * 60);

    if (diffInHours < 24) {
        return date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
    } else if (diffInHours < 48) {
        return 'Yesterday ' + date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
    } else {
        return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
    }
}

// Message form submission
$('#message-form').on('submit', function(e) {
    e.preventDefault();

    const message = $('#message-input').val().trim();
    if (!message || !currentConversationId) return;

    // Disable form while sending
    const submitBtn = $(this).find('button[type="submit"]');
    const originalHtml = submitBtn.html();
    submitBtn.prop('disabled', true).html('<i class="feather icon-loader"></i>');

    $.post('<?php echo e(route("provider.chat.send")); ?>', {
        conversation_id: currentConversationId,
        message: message,
        type: 'text',
        _token: '<?php echo e(csrf_token()); ?>'
    })
    .done(function(response) {
        if (response.status) {
            $('#message-input').val('');
            // Message will be added via Pusher event
        } else {
            alert('Failed to send message. Please try again.');
        }
    })
    .fail(function() {
        alert('Failed to send message. Please check your connection.');
    })
    .always(function() {
        // Re-enable form
        submitBtn.prop('disabled', false).html(originalHtml);
    });
});

// Typing indicator
$('#message-input').on('input', function() {
    if (!currentConversationId) return;
    
    // Send typing indicator
    $.post('<?php echo e(route("provider.chat.typing")); ?>', {
        conversation_id: currentConversationId,
        is_typing: true,
        _token: '<?php echo e(csrf_token()); ?>'
    });
    
    // Clear previous timer
    clearTimeout(typingTimer);
    
    // Set timer to stop typing indicator
    typingTimer = setTimeout(function() {
        $.post('<?php echo e(route("provider.chat.typing")); ?>', {
            conversation_id: currentConversationId,
            is_typing: false,
            _token: '<?php echo e(csrf_token()); ?>'
        });
    }, 1000);
});

function updateNotificationCountFromServerFromServer() {
    $.get('<?php echo e(route("provider.chat.notification-count")); ?>')
        .done(function(response) {
            if (response.status) {
                // Update the notification count in the navbar
                const notificationCount = $('#notification-count');
                const notificationCountHeader = $('.notification-count-header');

                if (response.count > 0) {
                    notificationCount.text(response.count).show();
                    notificationCountHeader.text(response.count + ' <?php echo e(__("provider.new")); ?>');
                } else {
                    notificationCount.hide();
                    notificationCountHeader.text('0 <?php echo e(__("provider.new")); ?>');
                }
            }
        })
        .fail(function() {
            console.log('Failed to update notification count');
        });
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('provider.layout.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\Workstation\Taswk\elhafla\resources\views/provider/chat/index.blade.php ENDPATH**/ ?>