<?php

namespace App\Http\Controllers\Provider;

use App\Http\Controllers\Controller;
use App\Http\Resources\Chat\ConversationResource;
use App\Http\Resources\Chat\MessageResource;
use App\Models\Conversation;
use App\Models\MessageConversation;
use App\Models\User;
use App\Events\MessageSent;
use App\Events\MessageRead;
use App\Events\UserTyping;
use App\Events\NewMessageNotification as NewMessageNotificationEvent;
use App\Notifications\NewMessageNotification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ChatController extends Controller
{
    /**
     * Display chat interface
     */
    public function index()
    {
        $provider = auth()->guard('provider')->user();
        $providerData = $provider->provider;

        if (!$providerData) {
            return redirect()->route('provider.complete-register')
                ->with('warning', __('provider.complete_registration_first'));
        }

        return view('provider.chat.index', compact('provider', 'providerData'));
    }

    /**
     * Get conversations for provider
     */
    public function getConversations()
    {
        $providerId = auth()->guard('provider')->id();

        $conversations = Conversation::with(['userOne.providerDetails', 'userTwo.providerDetails', 'lastMessage' , 'messages'])
            ->where('user_one_id', $providerId)
            ->orWhere('user_two_id', $providerId)
            ->withCount(['messages as unread_messages_count' => function ($query) use ($providerId) {
                $query->where('is_read', false)->where('sender_id', '!=', $providerId);
            }])
            ->orderBy('updated_at', 'desc')
            ->get();

        return response()->json([
            'status' => true,
            'conversations' => ConversationResource::collection($conversations)
        ]);
    }

    /**
     * Get messages for a specific conversation
     */
    public function getMessages(Request $request, $conversationId)
    {
        $providerId = auth()->guard('provider')->id();

        $conversation = Conversation::with(['userOne', 'userTwo'])
            ->where('id', $conversationId)
            ->where(function ($q) use ($providerId) {
                $q->where('user_one_id', $providerId)->orWhere('user_two_id', $providerId);
            })
            ->first();

        if (!$conversation) {
            return response()->json(['message' => 'Conversation not found'], 404);
        }

        // Get paginated messages
        $page = $request->get('page', 1);
        $perPage = $request->get('per_page', 50);

        $messages = MessageConversation::with(['sender.providerDetails'])
            ->where('conversation_id', $conversationId)
            ->orderBy('created_at', 'desc')
            ->paginate($perPage, ['*'], 'page', $page);

        // Mark messages as read
        MessageConversation::where('conversation_id', $conversationId)
            ->where('sender_id', '!=', $providerId)
            ->where('is_read', false)
            ->update(['is_read' => true]);

        // Broadcast read status
        $messageIds = MessageConversation::where('conversation_id', $conversationId)
            ->where('sender_id', '!=', $providerId)
            ->where('is_read', true)
            ->pluck('id')
            ->toArray();

        if (!empty($messageIds)) {
            broadcast(new MessageRead($conversationId, $providerId, $messageIds));
        }

        return response()->json([
            'status' => true,
            'conversation' => new ConversationResource($conversation),
            'messages' => [
                'data' => MessageResource::collection($messages->items()),
                'current_page' => $messages->currentPage(),
                'last_page' => $messages->lastPage(),
                'per_page' => $messages->perPage(),
                'total' => $messages->total(),
                'has_more' => $messages->hasMorePages(),
            ]
        ]);
    }

    /**
     * Send a message
     */
    public function sendMessage(Request $request)
    {
        $request->validate([
            'conversation_id' => 'required|exists:conversations,id',
            'message' => 'required|string|max:1000',
            'type' => 'required|string|in:text,image,file'
        ]);

        $providerId = auth()->guard('provider')->id();
        
        // Verify provider is part of conversation
        $conversation = Conversation::where('id', $request->conversation_id)
            ->where(function ($q) use ($providerId) {
                $q->where('user_one_id', $providerId)->orWhere('user_two_id', $providerId);
            })->first();

        if (!$conversation) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        // Create message
        $message = MessageConversation::create([
            'conversation_id' => $request->conversation_id,
            'sender_id' => $providerId,
            'message' => $request->message,
            'type' => $request->type,
            'is_read' => false,
        ]);

        // Update conversation timestamp
        $conversation->touch();

        // Broadcast the message
        $sender = User::find($providerId);
        broadcast(new MessageSent($message, $sender, $conversation->id));

        // Send notification to the receiver
        $receiverId = $conversation->user_one_id === $providerId ? $conversation->user_two_id : $conversation->user_one_id;
        $receiver = User::find($receiverId);

        // Broadcast real-time notification
        broadcast(new NewMessageNotificationEvent($message, $sender, $receiver));

        // Send database notification with FCM
        $receiver->notify(new NewMessageNotification($message, $sender, $conversation->id));

        return response()->json([
            'status' => true,
            'message' => 'Message sent successfully',
            'data' => new MessageResource($message)
        ]);
    }

    /**
     * Send typing indicator
     */
    public function typing(Request $request)
    {
        $request->validate([
            'conversation_id' => 'required|exists:conversations,id',
            'is_typing' => 'boolean'
        ]);

        $providerId = auth()->guard('provider')->id();
        $isTyping = $request->input('is_typing', true);
        
        // Verify provider is part of conversation
        $conversation = Conversation::where('id', $request->conversation_id)
            ->where(function ($q) use ($providerId) {
                $q->where('user_one_id', $providerId)->orWhere('user_two_id', $providerId);
            })->first();

        if (!$conversation) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $user = User::find($providerId);
        broadcast(new UserTyping($user, $request->conversation_id, $isTyping));

        return response()->json([
            'status' => true,
            'message' => 'Typing status sent'
        ]);
    }


}
