<?php

namespace App\Http\Controllers\Api\Client;
use App\Http\Controllers\Controller;
use App\Http\Resources\ConversationResource;
use App\Http\Resources\Chat\ConversationResource as ChatConversationResource;
use App\Http\Resources\Chat\MessageResource;
use App\Models\Conversation;
use App\Models\MessageConversation;
use App\Models\User;
use App\Events\MessageSent;
use App\Events\MessageRead;
use App\Events\UserTyping;
use App\Events\NewMessageNotification as NewMessageNotificationEvent;
use App\Notifications\NewMessageNotification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Spatie\FlareClient\Api;
use App\Facades\Responder;

class ConversationController extends Controller
{
  
public function index()
{
    $authId = auth()->id();

    $conversations = Conversation::with(['userOne.providerDetails', 'userTwo.providerDetails'])
        ->where('user_one_id', $authId)
        ->orWhere('user_two_id', $authId)
        ->withCount(['messages as unread_messages_count' => function ($query) use ($authId) {
            $query->where('is_read', false)->where('sender_id', '!=', $authId);
        }])
        ->latest()
        ->get();

    return Responder::success(ConversationResource::collection($conversations));
}




    public function show($id, Request $request)
    {
        $authId = auth()->id();
        $conversation = Conversation::with(['userOne', 'userTwo', 'messages.sender.providerDetails'])
            ->findOrFail($id);

        if ($conversation->user_one_id !== $authId && $conversation->user_two_id !== $authId) {
            return Responder::error('Unauthorized', [], 403);
        }

        // Mark messages as read when viewing conversation
        MessageConversation::where('conversation_id', $id)
            ->where('sender_id', '!=', $authId)
            ->where('is_read', false)
            ->update(['is_read' => true]);

        return Responder::success(new ChatConversationResource($conversation));
    }















   public function latestMessage()
{
    $authId = auth()->id();

    $message = MessageConversation::whereHas('conversation', function ($q) use ($authId) {
        $q->where('user_one_id', $authId)->orWhere('user_two_id', $authId);
    })
    ->with('conversation')
    ->latest()
    ->first();

    if (!$message) {
        return Responder::error('لا توجد رسائل حالياً', null, 404);
    }

    return Responder::success([
        'conversation_id' => $message->conversation_id,
        'message_id'      => $message->id,
        'text'            => $message->message,
        'sender_id'       => $message->sender_id,
        'sent_at'         => $message->created_at->diffForHumans(),
    ]);
}

    // public function sendMessage(Request $request)
    // {
    //     $request->validate([
    //         'conversation_id' => 'required|exists:conversations,id',
    //         'message' => 'required|min:1|max:200',
    //     ]);

    //     $authId = Auth::id();
    //     $conversation = Conversation::findOrFail($request->conversation_id);

    //     if ($conversation->user_one_id !== $authId && $conversation->user_two_id !== $authId) {
    //         return response()->json(['message' => 'Unauthorized'], 403);
    //     }

    //     $message = MessageConversation::create([
    //         'conversation_id' => $conversation->id,
    //         'sender_id' => $authId,
    //         'message' => $request->message,
    //         'is_read' => false,
    //     ]);

    //     return response()->json([
    //         'status' => true,
    //         'message' => 'Message sent successfully',
    //         'data' => [
    //             'id' => $message->id,
    //             'message' => $message->message,
    //             'date' => $message->created_at->format('Y-m-d'),
    //             'time' => $message->created_at->format('H:i'),
    //         ]
    //     ]);
    // }


    // public function startOrGetConversation(Request $request)
    // {
    //     $data = $request->validate([
    //         'receiver_id' => ['required', 'exists:users,id'],
    //     ]);
    
    //     $authId = auth()->id();
    //     $receiverId = $data['receiver_id'];
    
    //     // لا يمكن بدء محادثة مع النفس
    //     if ($authId == $receiverId) {
    //         return response()->json([
    //             'status' => false,
    //             'message' => 'لا يمكن بدء محادثة مع نفسك',
    //         ], 422);
    //     }
    
    //     // البحث عن محادثة قديمة
    //     $conversation = Conversation::where(function ($q) use ($authId, $receiverId) {
    //         $q->where('user_one_id', $authId)->where('user_two_id', $receiverId);
    //     })->orWhere(function ($q) use ($authId, $receiverId) {
    //         $q->where('user_one_id', $receiverId)->where('user_two_id', $authId);
    //     })->first();
    
    //     // لو مفيش، أنشئ محادثة جديدة
    //     if (!$conversation) {
    //         $conversation = Conversation::create([
    //             'user_one_id' => $authId,
    //             'user_two_id' => $receiverId,
    //         ]);
    //     }
    
    //     return response()->json([
    //         'status' => true,
    //         'message' => 'Conversation ready',
    //         'conversation_id' => $conversation->id,
    //     ]);
    // }
    


    public function startOrSendMessage(Request $request)
    {
        $data = $request->validate([
            'receiver_id' => ['required', 'exists:users,id'],
            'message'     => ['nullable', 'string', 'min:1', 'max:200'],
            'type'        => ['nullable', 'string'],
            'image'       => ['nullable', 'image', 'max:2048'],
        ]);
    
        $authId = auth()->id();
        $receiverId = $data['receiver_id'];
        $data['type'] = 'text';
    
        if ($authId == $receiverId) {
            return Responder::error('لا يمكن بدء محادثة مع نفسك', [], 422);
        }
    
        $conversation = Conversation::where(function ($q) use ($authId, $receiverId) {
            $q->where('user_one_id', $authId)->where('user_two_id', $receiverId);
        })->orWhere(function ($q) use ($authId, $receiverId) {
            $q->where('user_one_id', $receiverId)->where('user_two_id', $authId);
        })->first();
    
        if (!$conversation) {
            $conversation = Conversation::create([
                'user_one_id' => $authId,
                'user_two_id' => $receiverId,
            ]);
        }
    
        $messageData = null;

        if (!empty($data['message']) || $request->hasFile('image')) {
            $message = MessageConversation::create([
                'conversation_id' => $conversation->id,
                'sender_id'       => $authId,
                'message'         => $data['message'] ?? null,
                'type'            => $data['type'],
                'is_read'         => false,
            ]);

            if ($request->hasFile('image')) {
                $message->addMedia($request->file('image'))
                    ->toMediaCollection('messages');
            }

            $sender = User::find($authId);
            broadcast(new MessageSent($message, $sender, $conversation->id));

            $receiverId = $conversation->user_one_id === $authId ? $conversation->user_two_id : $conversation->user_one_id;
            $receiver = User::find($receiverId);
            if($sender && $receiver) {
                // Broadcast real-time notification
                broadcast(new NewMessageNotificationEvent($message, $sender, $receiver));

                // Send database notification with FCM
                $receiver->notify(new NewMessageNotification($message, $sender, $conversation->id));
            }

            $messageData = [
                'id'      => $message->id,
                'message' => $message->message,
                'type'    => $message->type,
                'image'   => $message->getFirstMediaUrl('messages'),
                'date'    => $message->created_at->format('Y-m-d'),
                'time'    => $message->created_at->format('H:i'),
            ];
        }
    
        return Responder::success([
            'conversation_id'=> $conversation->id,
            'sent_message'   => $messageData,
        ] , ['message' => __('apis.success')]);
    }


    public function startConversationOnly(Request $request)
{
    $data = $request->validate([
        'receiver_id' => ['required', 'exists:users,id'],
    ]);

    $authId = auth()->id();
    $receiverId = $data['receiver_id'];

    if ($authId == $receiverId) {
        return Responder::error('لا يمكن بدء محادثة مع نفسك', [], 422);
    }

    $conversation = Conversation::where(function ($q) use ($authId, $receiverId) {
        $q->where('user_one_id', $authId)->where('user_two_id', $receiverId);
    })->orWhere(function ($q) use ($authId, $receiverId) {
        $q->where('user_one_id', $receiverId)->where('user_two_id', $authId);
    })->first();

    if (!$conversation) {
        $conversation = Conversation::create([
            'user_one_id' => $authId,
            'user_two_id' => $receiverId,
        ]);
    }

    return Responder::success([
        'message'         => 'تم بدء المحادثة بنجاح',
        'conversation_id' => $conversation->id,
    ]);
}

    /**
     * Mark messages as read
     */
    public function markAsRead(Request $request, $conversationId)
    {
        $authId = auth()->id();

        $conversation = Conversation::where('id', $conversationId)
            ->where(function ($q) use ($authId) {
                $q->where('user_one_id', $authId)->orWhere('user_two_id', $authId);
            })->first();

        if (!$conversation) {
            return Responder::error('Conversation not found', [], 404);
        }

        $messageIds = MessageConversation::where('conversation_id', $conversationId)
            ->where('sender_id', '!=', $authId)
            ->where('is_read', false)
            ->pluck('id')
            ->toArray();

        MessageConversation::where('conversation_id', $conversationId)
            ->where('sender_id', '!=', $authId)
            ->where('is_read', false)
            ->update(['is_read' => true]);

        broadcast(new MessageRead($conversationId, $authId, $messageIds));

        return Responder::success([
            'message' => 'Messages marked as read',
            'marked_count' => count($messageIds)
        ]);
    }

    /**
     * Send typing indicator
     */
    public function typing(Request $request, $conversationId)
    {
        $authId = auth()->id();
        $isTyping = $request->input('is_typing', true);

        $conversation = Conversation::where('id', $conversationId)
            ->where(function ($q) use ($authId) {
                $q->where('user_one_id', $authId)->orWhere('user_two_id', $authId);
            })->first();

        if (!$conversation) {
            return Responder::error('Conversation not found', [], 404);
        }

        $user = User::find($authId);
        broadcast(new UserTyping($user, $conversationId, $isTyping));

        return Responder::success([
            'message' => 'Typing status sent'
        ]);
    }




// public function send(Request $request)
// {
//     $request->validate([
//         'conversation_id' => 'required|exists:conversations,id',
//         'message' => 'required|string',
//         'images' => 'nullable|array',
//         'images.*' => 'image|mimes:jpeg,png,jpg,gif,svg|max:2048',
//     ]);

//     $authId = auth()->id();
//     $conversationId = $request->conversation_id;

//     $conversation = Conversation::findOrFail($conversationId);

//     if ($conversation->user_one_id !== $authId && $conversation->user_two_id !== $authId) {
//         return response()->json(['message' => 'Unauthorized'], 403);
//     }

//     if ($request->hasFile('images')) {
//         if ($request->message) {
//             $message = MessageConversation::create([
//                 'conversation_id' => $conversationId,
//                 'sender_id' => $authId,
//                 'message' => $request->message,
//                 'is_read' => false,
//             ]);

//             foreach ($request->file('images') as $image) {
//                 $message->addMedia($image)->toMediaCollection();
//             }
//         } else {
//             foreach ($request->file('images') as $image) {
//                 $message = MessageConversation::create([
//                     'conversation_id' => $conversationId,
//                     'sender_id' => $authId,
//                     'message' => null,
//                     'is_read' => false,
//                 ]);
//                 $message->addMedia($image)->toMediaCollection();
//             }
//         }
//     } else {
//         MessageConversation::create([
//             'conversation_id' => $conversationId,
//             'sender_id' => $authId,
//             'message' => $request->message,
//             'is_read' => false,
//         ]);
//     }

//     return response()->json([
//         'status' => true,
//         'message' => 'Message sent successfully',
//     ]);
// }



}