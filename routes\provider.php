<?php

use Illuminate\Support\Facades\Route;

Route::group([
    'prefix'     => 'provider',
    'namespace'  => 'Provider',
    'as'         => 'provider.',
    'middleware' => ['web-cors'],
], function () {

    Route::get('/lang/{lang}', 'AuthController@SetLanguage');

    Route::get('login', 'AuthController@showLoginForm')->name('login')->middleware('guest:provider');
    Route::post('login', 'AuthController@login')->name('login.submit');
    Route::get('logout', 'AuthController@logout')->name('logout');

    Route::get('register', 'AuthController@showRegisterForm')->name('register')->middleware('guest:provider');
    Route::post('register', 'AuthController@register')->name('register.submit');
    Route::post('send-verification-code', 'AuthController@sendVerificationCode')->name('send-verification-code');
    Route::post('verify-code', 'Auth<PERSON>ontroller@verifyCode')->name('verify-code');
    Route::post('store-business-session', 'AuthController@storeBusinessSession')->name('store.business.session');
    Route::get('complete-register', 'AuthController@showCompleteRegisterForm')->name('complete-register')->middleware('auth:provider');
    Route::post('complete-register', 'AuthController@completeRegister')->name('complete-register.submit');

    Route::get('forget-password', 'AuthController@showForgetPasswordForm')->name('forget-password');
    Route::post('forget-password', 'AuthController@forgetPassword')->name('forget-password.submit');
    Route::post('verify-reset-code', 'AuthController@verifyResetCode')->name('verify-reset-code');

    Route::get('reset-password/{provider}', 'AuthController@showResetPasswordForm')->name('show.reset-password');
    Route::post('reset-password', 'AuthController@resetPassword')->name('reset-password');

    // Route::post('getCities', 'CityController@getCities')->name('getCities');

    // Plans and Payment Routes (accessible after registration)
    Route::group(['middleware' => ['provider', 'provider-lang']], function () {
        Route::get('plans', [
            'uses' => 'PlanController@index',
            'as' => 'plans.index',
            'title' => 'choose_plan'
        ]);

        Route::post('plans/select', [
            'uses' => 'PlanController@selectPlan',
            'as' => 'plans.select',
            'title' => 'select_plan'
        ]);

        Route::get('payment/callback', [
            'uses' => 'PlanController@paymentCallback',
            'as' => 'payment.callback',
            'title' => 'payment_callback'
        ]);

        Route::get('payment/success', [
            'uses' => 'PlanController@paymentSuccess',
            'as' => 'payment.success',
            'title' => 'payment_success'
        ]);

        Route::get('payment/error', [
            'uses' => 'PlanController@paymentError',
            'as' => 'payment.error',
            'title' => 'payment_error'
        ]);
    });

    Route::group(['middleware' => ['provider', 'check-provider-role', 'provider-lang']], function () {
        /*------------ start Of profile----------*/
        Route::get('profile', [
            'uses'      => 'HomeController@profile',
            'as'        => 'profile',
            'title'     => 'profile',
            'sub_route' => true,
            'type'      => 'parent',
            'child'     => ['profile.update_password', 'profile.update'],
        ]);

        Route::put('profile-update', [
            'uses'  => 'HomeController@updateProfile',
            'as'    => 'profile.update',
            'title' => 'update_profile',
        ]);
        Route::put('profile-update-password', [
            'uses'  => 'HomeController@updatePassword',
            'as'    => 'profile.update_password',
            'title' => 'update_password',
        ]);
        /*------------ end Of profile----------*/

        /*------------ start Of Dashboard----------*/
        Route::get('dashboard', [
            'uses'      => 'HomeController@dashboard',
            'as'        => 'dashboard',
            'icon'      => '<i class="feather icon-home"></i>',
            'title'     => 'main_page',
            'sub_route' => false,
            'type'      => 'parent',
        ]);
        /*------------ end Of dashboard ----------*/

        /*------------ start Of Chat ----------*/
        Route::get('chat', [
            'uses'      => 'ChatController@index',
            'as'        => 'chat.index',
            'title'     => 'chat',
            'icon'      => '<i class="feather icon-message-circle"></i>',
            'sub_route' => false,
            'child'     => ['chat.conversations', 'chat.messages', 'chat.send', 'chat.typing']
        ]);

        Route::get('chat/conversations', [
            'uses' => 'ChatController@getConversations',
            'as' => 'chat.conversations',
            'title' => 'get_conversations'
        ]);

        Route::get('chat/messages/{conversationId}', [
            'uses' => 'ChatController@getMessages',
            'as' => 'chat.messages',
            'title' => 'get_messages'
        ]);

        Route::post('chat/send', [
            'uses' => 'ChatController@sendMessage',
            'as' => 'chat.send',
            'title' => 'send_message'
        ]);

        Route::post('chat/typing', [
            'uses' => 'ChatController@typing',
            'as' => 'chat.typing',
            'title' => 'typing_indicator'
        ]);
        /*------------ end Of Chat ----------*/

        /*------------ start Of Services ----------*/
        Route::get('services', [
            'uses'      => 'ServiceController@index',
            'as'        => 'services.index',
            'title'     => 'services',
            'icon'      => '<i class="feather icon-briefcase"></i>',
            'sub_route' => false,
            'child'     => ['services.create', 'services.store','services.edit', 'services.update', 'services.show', 'services.delete', 'services.deleteAll']
        ]);

        Route::get('services/create', [
            'uses'  => 'ServiceController@create',
            'as'    => 'services.create',
            'title' => 'add_service_page'
        ]);

        Route::post('services/store', [
            'uses'  => 'ServiceController@store',
            'as'    => 'services.store',
            'title' => 'add_service'
        ]);

        Route::get('services/{id}/edit', [
            'uses'  => 'ServiceController@edit',
            'as'    => 'services.edit',
            'title' => 'edit_service_page'
        ]);

        Route::put('services/{id}', [
            'uses'  => 'ServiceController@update',
            'as'    => 'services.update',
            'title' => 'edit_service'
        ]);

        Route::get('services/{id}', [
            'uses'  => 'ServiceController@show',
            'as'    => 'services.show',
            'title' => 'show_service'
        ]);

        Route::delete('services/{id}', [
            'uses'  => 'ServiceController@destroy',
            'as'    => 'services.delete',
            'title' => 'delete_service'
        ]);

        Route::post('services/deleteAll', [
            'uses'  => 'ServiceController@deleteAll',
            'as'    => 'services.deleteAll',
            'title' => 'delete_all_services'
        ]);

        Route::post('services/{id}/toggle-status', [
            'uses'  => 'ServiceController@toggleStatus',
            'as'    => 'services.toggle-status',
            'title' => 'toggle_service_status'
        ]);
        /*------------ end Of Services ----------*/

        /*------------ start Of Subscriptions ----------*/
        Route::get('subscriptions', [
            'uses'      => 'SubscriptionController@index',
            'as'        => 'subscriptions.index',
            'title'     => 'subscriptions',
            'icon'      => '<i class="feather icon-credit-card"></i>',
            'sub_route' => false,
            'child'     => ['subscriptions.create', 'subscriptions.store', 'subscriptions.show']
        ]);

        Route::get('subscriptions/create', [
            'uses'  => 'SubscriptionController@create',
            'as'    => 'subscriptions.create',
            'title' => 'add_subscription_page'
        ]);

        Route::post('subscriptions/store', [
            'uses'  => 'SubscriptionController@store',
            'as'    => 'subscriptions.store',
            'title' => 'add_subscription'
        ]);

        Route::get('subscriptions/{id}', [
            'uses'  => 'SubscriptionController@show',
            'as'    => 'subscriptions.show',
            'title' => 'show_subscription'
        ]);

        Route::post('subscriptions/{id}/renew', [
            'uses'  => 'SubscriptionController@renew',
            'as'    => 'subscriptions.renew',
            'title' => 'renew_subscription'
        ]);

        Route::post('subscriptions/{id}/cancel', [
            'uses'  => 'SubscriptionController@cancel',
            'as'    => 'subscriptions.cancel',
            'title' => 'cancel_subscription'
        ]);

        Route::get('api/plans', [
            'uses'  => 'SubscriptionController@getPlans',
            'as'    => 'api.plans',
            'title' => 'get_plans'
        ]);

        Route::get('api/payment-methods', [
            'uses'  => 'SubscriptionController@getPaymentMethods',
            'as'    => 'api.payment-methods',
            'title' => 'get_payment_methods'
        ]);
        /*------------ end Of Subscriptions ----------*/

        /*------------ start Of Statistics ----------*/
        Route::get('statistics', [
            'uses'      => 'StatisticsController@index',
            'as'        => 'statistics.index',
            'title'     => 'statistics',
            'icon'      => '<i class="feather icon-bar-chart-2"></i>',
            'sub_route' => false,
            'type'      => 'parent',
        ]);
        /*------------ end Of Statistics ----------*/

        /*------------ start Of Reviews ----------*/
        Route::get('reviews', [
            'uses'      => 'ReviewController@index',
            'as'        => 'reviews.index',
            'title'     => 'reviews',
            'icon'      => '<i class="feather icon-star"></i>',
            'sub_route' => false,
            'child'     => ['reviews.show', 'reviews.reply']
        ]);

        Route::get('reviews/{id}', [
            'uses'  => 'ReviewController@show',
            'as'    => 'reviews.show',
            'title' => 'show_review'
        ]);

        Route::post('reviews/{id}/reply', [
            'uses'  => 'ReviewController@reply',
            'as'    => 'reviews.reply',
            'title' => 'reply_to_review'
        ]);
        /*------------ end Of Reviews ----------*/

        /*------------ start Of Working Hours ----------*/
        Route::get('working-hours', [
            'uses'      => 'WorkingHourController@index',
            'as'        => 'working-hours.index',
            'title'     => 'working_hours',
            'icon'      => '<i class="feather icon-clock"></i>',
            'sub_route' => false,
            'child'     => ['working-hours.store', 'working-hours.update']
        ]);

        Route::post('working-hours', [
            'uses'  => 'WorkingHourController@store',
            'as'    => 'working-hours.store',
            'title' => 'save_working_hours'
        ]);

        Route::put('working-hours/{id}', [
            'uses'  => 'WorkingHourController@update',
            'as'    => 'working-hours.update',
            'title' => 'update_working_hours'
        ]);

        Route::delete('working-hours/{id}', [
            'uses'  => 'WorkingHourController@destroy',
            'as'    => 'working-hours.destroy',
            'title' => 'delete_working_hours'
        ]);
        /*------------ end Of Working Hours ----------*/

        /*------------ start Of Orders ----------*/
        Route::get('orders', [
            'uses'      => 'OrderController@index',
            'as'        => 'orders.index',
            'title'     => 'orders',
            'icon'      => '<i class="feather icon-shopping-cart"></i>',
            'sub_route' => false,
            'child'     => ['orders.show', 'orders.update-status']
        ]);

        Route::get('orders/{id}', [
            'uses'  => 'OrderController@show',
            'as'    => 'orders.show',
            'title' => 'show_order'
        ]);

        Route::put('orders/{id}/status', [
            'uses'  => 'OrderController@updateStatus',
            'as'    => 'orders.update-status',
            'title' => 'update_order_status'
        ]);
        /*------------ end Of Orders ----------*/

        /*------------ start Of Notifications ----------*/
        Route::get('notifications', [
            'uses'      => 'NotificationController@index',
            'as'        => 'notifications.index',
            'title'     => 'notifications',
            'icon'      => '<i class="feather icon-bell"></i>',
            'sub_route' => false,
            'child'     => ['notifications.get', 'notifications.mark-all-read', 'notifications.mark-read', 'notifications.delete', 'notifications.delete-all', 'notifications.click']
        ]);

        Route::get('notifications/get', [
            'uses'  => 'NotificationController@getNotifications',
            'as'    => 'notifications.get',
            'title' => 'get_notifications'
        ]);

        Route::post('notifications/mark-all-read', [
            'uses'  => 'NotificationController@markAllAsRead',
            'as'    => 'notifications.mark-all-read',
            'title' => 'mark_all_notifications_read'
        ]);

        Route::post('notifications/{id}/mark-read', [
            'uses'  => 'NotificationController@markAsRead',
            'as'    => 'notifications.mark-read',
            'title' => 'mark_notification_read'
        ]);

        Route::get('notifications/{id}/click', [
            'uses'  => 'NotificationController@handleClick',
            'as'    => 'notifications.click',
            'title' => 'handle_notification_click'
        ]);

        Route::delete('notifications/{id}', [
            'uses'  => 'NotificationController@delete',
            'as'    => 'notifications.delete',
            'title' => 'delete_notification'
        ]);

        Route::delete('notifications/delete-all', [
            'uses'  => 'NotificationController@deleteAll',
            'as'    => 'notifications.delete-all',
            'title' => 'delete_all_notifications'
        ]);
        /*------------ end Of Notifications ----------*/

        /*------------ start Of Settings ----------*/
        Route::get('settings', [
            'uses'      => 'SettingController@index',
            'as'        => 'settings.index',
            'title'     => 'settings',
            'icon'      => '<i class="feather icon-settings"></i>',
            'sub_route' => false,
            'type'      => 'parent',
        ]);

        Route::put('settings', [
            'uses'  => 'SettingController@update',
            'as'    => 'settings.update',
            'title' => 'update_settings'
        ]);
        /*------------ end Of Settings ----------*/
    });
});
