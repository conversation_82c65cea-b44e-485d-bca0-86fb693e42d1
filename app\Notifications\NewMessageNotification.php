<?php

namespace App\Notifications;

use App\Models\MessageConversation;
use App\Models\User;
use App\Traits\Firebase;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class NewMessageNotification extends Notification
{
    use Queueable, Firebase;

    protected $message;
    protected $sender;
    protected $conversationId;

    /**
     * Create a new notification instance.
     */
    public function __construct(MessageConversation $message, User $sender, $conversationId)
    {
        $this->message = $message;
        $this->sender = $sender;
        $this->conversationId = $conversationId;
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via($notifiable)
    {
        return ['database'];
    }

    /**
     * Get the array representation of the notification.
     */
    public function toArray($notifiable)
    {
        // Determine if sender is a provider
        $isProvider = $this->sender->type === 'provider';
        $senderName = $isProvider 
            ? ($this->sender->providerDetails->name ?? $this->sender->name)
            : $this->sender->name;

        $data = [
            'conversation_id' => $this->conversationId,
            'message_id' => $this->message->id,
            'sender_id' => $this->sender->id,
            'sender_name' => $senderName,
            'sender_type' => $this->sender->type ?? 'client',
            'message' => $this->message->message,
            'message_type' => $this->message->type,
            'type' => 'new_message',
            'title' => [
                'ar' => 'رسالة جديدة من ' . $senderName,
                'en' => 'New message from ' . $senderName,
            ],
            'body' => [
                'ar' => $this->message->message,
                'en' => $this->message->message,
            ],
            'created_at' => $this->message->created_at->toISOString(),
        ];

        // Send FCM notification if user has devices
        $tokens = [];
        $types = [];
        
        if (count($notifiable->devices)) {
            foreach ($notifiable->devices as $device) {
                $tokens[] = $device->device_id;
                $types[] = $device->device_type;
            }
            $this->sendFcmNotification($tokens, $types, $data, $notifiable->lang ?? 'ar');
        }

        return $data;
    }
}
