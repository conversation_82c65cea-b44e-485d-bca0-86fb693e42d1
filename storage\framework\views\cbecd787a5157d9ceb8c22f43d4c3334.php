<div class="position-relative">
    
    <table class="table" id="tab">
        <thead>
            <tr>
                <th>
                    <label class="container-checkbox">
                        <input type="checkbox" id="checkedAll">
                        <span class="checkmark"></span>
                    </label>
                </th>
                <th><?php echo e(__('provider.plan')); ?></th>
                <th><?php echo e(__('provider.duration')); ?></th>
                <th><?php echo e(__('provider.price')); ?></th>
                <th><?php echo e(__('provider.status')); ?></th>
                <th><?php echo e(__('provider.payment_status')); ?></th>
                <th><?php echo e(__('provider.expiry_date')); ?></th>
                <th><?php echo e(__('provider.created_at')); ?></th>
                <th><?php echo e(__('provider.control')); ?></th>
            </tr>
        </thead>
        <tbody>
            <?php $__currentLoopData = $subscriptions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $subscription): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <tr class="delete_row">
                    <td class="text-center">
                        <label class="container-checkbox">
                            <input type="checkbox" class="checkSingle" id="<?php echo e($subscription->id); ?>">
                            <span class="checkmark"></span>
                        </label>
                    </td>

                    <td>
                        <div class="d-flex align-items-center">
                            <div>
                                <h6 class="mb-0"><?php echo e($subscription->plan->name); ?></h6>
                                <small class="text-muted"><?php echo e(\Illuminate\Support\Str::limit($subscription->plan->description ?? '', 40)); ?></small>
                            </div>
                        </div>
                    </td>
                        <td>
                            <span class="text-muted"><?php echo e($subscription->plan->duration); ?> <?php echo e(__('provider.days')); ?></span>
                        </td>
                        <td>
                            <span class="font-weight-bold"><?php echo e(number_format($subscription->plan->price, 2)); ?> <?php echo e(__('provider.currency')); ?></span>
                            <?php if($subscription->plan->price_before_discount && $subscription->plan->price_before_discount > $subscription->plan->price): ?>
                                <br><small class="text-muted"><del><?php echo e(number_format($subscription->plan->price_before_discount, 2)); ?> <?php echo e(__('provider.currency')); ?></del></small>
                            <?php endif; ?>
                        </td>
                        <td>
                            <?php if($subscription->status === 'active'): ?>
                                <span class="badge badge-success"><?php echo e(__('provider.active')); ?></span>
                            <?php elseif($subscription->status === 'pending'): ?>
                                <span class="badge badge-warning"><?php echo e(__('provider.pending')); ?></span>
                            <?php else: ?>
                                <span class="badge badge-secondary"><?php echo e(__('provider.expired')); ?></span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <?php if($subscription->payment_status === 'paid'): ?>
                                <span class="badge badge-success"><?php echo e(__('provider.paid')); ?></span>
                            <?php elseif($subscription->payment_status === 'pending'): ?>
                                <span class="badge badge-warning"><?php echo e(__('provider.pending')); ?></span>
                            <?php else: ?>
                                <span class="badge badge-danger"><?php echo e(__('provider.cancelled')); ?></span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <?php if($subscription->expiry_date): ?>
                                <span class="text-muted"><?php echo e($subscription->expiry_date->format('M d, Y')); ?></span>
                                <?php if($subscription->status === 'active' && $subscription->expiry_date->diffInDays() <= 7): ?>
                                    <br><small class="text-warning"><?php echo e(__('provider.expires_soon')); ?></small>
                                <?php endif; ?>
                            <?php else: ?>
                                <span class="text-muted"><?php echo e(__('provider.not_set')); ?></span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <span class="text-muted"><?php echo e($subscription->created_at->format('M d, Y')); ?></span>
                        </td>
                    <td>
                        <div class="btn-group" role="group">
                            <a href="<?php echo e(route('provider.subscriptions.show', $subscription->id)); ?>"
                               class="btn btn-sm btn-info" title="<?php echo e(__('provider.view')); ?>">
                                <i class="feather icon-eye"></i>
                            </a>

                            <?php if($subscription->plan->price == 0): ?>
                                
                                <?php if($subscription->status === 'pending'): ?>
                                    <button type="button" class="btn btn-sm btn-danger"
                                            onclick="cancelSubscription(<?php echo e($subscription->id); ?>)"
                                            title="<?php echo e(__('provider.cancel')); ?>">
                                        <i class="feather icon-x"></i>
                                    </button>
                                <?php endif; ?>
                                <button type="button" class="btn btn-sm btn-primary"
                                        onclick="upgradeSubscription(<?php echo e($subscription->id); ?>)"
                                        title="<?php echo e(__('provider.upgrade')); ?>">
                                    <i class="feather icon-arrow-up"></i>
                                </button>
                            <?php else: ?>
                                
                                <?php if($subscription->status === 'active'): ?>
                                    <button type="button" class="btn btn-sm btn-success"
                                            onclick="renewSubscription(<?php echo e($subscription->id); ?>)"
                                            title="<?php echo e(__('provider.renew')); ?>">
                                        <i class="feather icon-refresh-cw"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-primary"
                                            onclick="upgradeSubscription(<?php echo e($subscription->id); ?>)"
                                            title="<?php echo e(__('provider.upgrade')); ?>">
                                        <i class="feather icon-arrow-up"></i>
                                    </button>
                                <?php elseif($subscription->status === 'expired'): ?>
                                    <button type="button" class="btn btn-sm btn-success"
                                            onclick="renewSubscription(<?php echo e($subscription->id); ?>)"
                                            title="<?php echo e(__('provider.renew')); ?>">
                                        <i class="feather icon-refresh-cw"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-primary"
                                            onclick="upgradeSubscription(<?php echo e($subscription->id); ?>)"
                                            title="<?php echo e(__('provider.upgrade')); ?>">
                                        <i class="feather icon-arrow-up"></i>
                                    </button>
                                <?php elseif($subscription->status === 'pending'): ?>
                                    <button type="button" class="btn btn-sm btn-danger"
                                            onclick="cancelSubscription(<?php echo e($subscription->id); ?>)"
                                            title="<?php echo e(__('provider.cancel')); ?>">
                                        <i class="feather icon-x"></i>
                                    </button>
                                <?php endif; ?>
                            <?php endif; ?>
                        </div>
                    </td>
                </tr>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </tbody>
    </table>

    
    <?php if($subscriptions && $subscriptions->hasPages()): ?>
        <div class="d-flex justify-content-center mt-3">
            <?php echo e($subscriptions->appends(request()->all())->links()); ?>

        </div>
    <?php endif; ?>
</div>

<?php if($subscriptions->count() == 0): ?>
    <div class="d-flex flex-column align-items-center justify-content-center" style="height: 300px;">
        <i class="feather icon-credit-card text-muted" style="font-size: 4rem;"></i>
        <h4 class="mt-3 text-muted"><?php echo e(__('provider.no_subscriptions_found')); ?></h4>
        <p class="text-muted"><?php echo e(__('provider.no_subscriptions_desc')); ?></p>
       
    </div>
<?php endif; ?>
<?php /**PATH D:\Workstation\Taswk\elhafla\resources\views/provider/subscriptions/table.blade.php ENDPATH**/ ?>