

<?php $__env->startSection('css'); ?>
    <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/app-assets/css-rtl/plugins/forms/validation/form-validation.css')); ?>">
    <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/app-assets/vendors/css/extensions/sweetalert2.min.css')); ?>">
    <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/index_page.css')); ?>">
    <style>
        .subscription-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border-radius: 10px;
        }
        .subscription-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        .current-subscription {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .stats-card {
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .status-badge {
            font-size: 0.9rem;
            padding: 6px 12px;
        }
    </style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <!-- Current Subscription Alert -->
    <?php if($currentSubscription): ?>
        <div class="row">
            <div class="col-12">
                <div class="card current-subscription">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <h4 class="text-white mb-2"><?php echo e(__('provider.current_subscription')); ?></h4>
                                <p class="text-white-75 mb-2">
                                    <?php echo e(__('provider.plan')); ?>: <strong><?php echo e($currentSubscription->plan->name); ?></strong>
                                </p>
                                <p class="text-white-75 mb-0">
                                    <?php echo e(__('provider.expires_on')); ?>: <strong><?php echo e($currentSubscription->expiry_date->format('M d, Y')); ?></strong>
                                    <?php if($currentSubscription->expiry_date->diffInDays() <= 7): ?>
                                        <span class="badge badge-warning ml-2"><?php echo e(__('provider.expires_soon')); ?></span>
                                    <?php endif; ?>
                                </p>
                            </div>
                            <div class="col-md-4 text-md-right text-center">
                                <div class="text-white mb-2">
                                    <h3><?php echo e($currentSubscription->expiry_date->diffInDays()); ?></h3>
                                    <small><?php echo e(__('provider.days_remaining')); ?></small>
                                </div>
                                <?php if($currentSubscription->plan->price == 0): ?>
                                    
                                    <button type="button" class="btn btn-light" onclick="upgradeSubscription(<?php echo e($currentSubscription->id); ?>)">
                                        <i class="feather icon-arrow-up"></i> <?php echo e(__('provider.upgrade')); ?>

                                    </button>
                                <?php else: ?>
                                    
                                    <button type="button" class="btn btn-light" onclick="renewSubscription(<?php echo e($currentSubscription->id); ?>)">
                                        <i class="feather icon-refresh-cw"></i> <?php echo e(__('provider.renew')); ?>

                                    </button>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php else: ?>
        <div class="row">
            <div class="col-12">
                <div class="alert alert-warning">
                    <h4 class="alert-heading"><?php echo e(__('provider.no_active_subscription')); ?></h4>
                    <p><?php echo e(__('provider.no_active_subscription_desc')); ?></p>
                    <hr>
                    <a href="<?php echo e(route('provider.subscriptions.create')); ?>" class="btn btn-primary">
                        <i class="feather icon-plus"></i> <?php echo e(__('provider.subscribe_now')); ?>

                    </a>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- Statistics Cards -->
    <div class="row">
        <div class="col-xl-3 col-md-6 col-12">
            <div class="card stats-card">
                <div class="card-content">
                    <div class="card-body">
                        <div class="media d-flex">
                            <div class="align-self-center">
                                <i class="feather icon-credit-card primary font-large-2 float-left"></i>
                            </div>
                            <div class="media-body text-right">
                                <h3 class="primary"><?php echo e($stats['total_subscriptions']); ?></h3>
                                <span><?php echo e(__('provider.total_subscriptions')); ?></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 col-12">
            <div class="card stats-card">
                <div class="card-content">
                    <div class="card-body">
                        <div class="media d-flex">
                            <div class="align-self-center">
                                <i class="feather icon-check-circle success font-large-2 float-left"></i>
                            </div>
                            <div class="media-body text-right">
                                <h3 class="success"><?php echo e($stats['active_subscriptions']); ?></h3>
                                <span><?php echo e(__('provider.active_subscriptions')); ?></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 col-12">
            <div class="card stats-card">
                <div class="card-content">
                    <div class="card-body">
                        <div class="media d-flex">
                            <div class="align-self-center">
                                <i class="feather icon-clock warning font-large-2 float-left"></i>
                            </div>
                            <div class="media-body text-right">
                                <h3 class="warning"><?php echo e($stats['pending_subscriptions']); ?></h3>
                                <span><?php echo e(__('provider.pending_subscriptions')); ?></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 col-12">
            <div class="card stats-card">
                <div class="card-content">
                    <div class="card-body">
                        <div class="media d-flex">
                            <div class="align-self-center">
                                <i class="feather icon-x-circle danger font-large-2 float-left"></i>
                            </div>
                            <div class="media-body text-right">
                                <h3 class="danger"><?php echo e($stats['expired_subscriptions']); ?></h3>
                                <span><?php echo e(__('provider.expired_subscriptions')); ?></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Subscriptions Management -->
    <?php if (isset($component)) { $__componentOriginal781089cd478f3e09d520a65f160df974 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal781089cd478f3e09d520a65f160df974 = $attributes; } ?>
<?php $component = App\View\Components\Admin\Table::resolve(['datefilter' => 'true','order' => 'true','extrabuttons' => 'true','searchArray' => [
            'status' => [
                'input_type' => 'select',
                'rows' => [
                    '1' => [
                        'name' => __('provider.active'),
                        'id' => 'active',
                    ],
                    '2' => [
                        'name' => __('provider.pending'),
                        'id' => 'pending',
                    ],
                    '3' => [
                        'name' => __('provider.expired'),
                        'id' => 'expired',
                    ],
                ],
                'input_name' => __('provider.status'),
            ],
            'payment_status' => [
                'input_type' => 'select',
                'rows' => [
                    '1' => [
                        'name' => __('provider.pending'),
                        'id' => 'pending',
                    ],
                    '2' => [
                        'name' => __('provider.paid'),
                        'id' => 'paid',
                    ],
                    '3' => [
                        'name' => __('provider.cancelled'),
                        'id' => 'cancelled',
                    ],
                ],
                'input_name' => __('provider.payment_status'),
            ],
        ]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('admin.table'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\Admin\Table::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>

     <?php $__env->slot('extrabuttonsdiv', null, []); ?> 
        
     <?php $__env->endSlot(); ?>
         <?php $__env->slot('tableContent', null, []); ?> 
            <div class="table_content_append card">
                
            </div>
         <?php $__env->endSlot(); ?>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal781089cd478f3e09d520a65f160df974)): ?>
<?php $attributes = $__attributesOriginal781089cd478f3e09d520a65f160df974; ?>
<?php unset($__attributesOriginal781089cd478f3e09d520a65f160df974); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal781089cd478f3e09d520a65f160df974)): ?>
<?php $component = $__componentOriginal781089cd478f3e09d520a65f160df974; ?>
<?php unset($__componentOriginal781089cd478f3e09d520a65f160df974); ?>
<?php endif; ?>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('js'); ?>
    <script src="<?php echo e(asset('admin/app-assets/vendors/js/extensions/sweetalert2.all.min.js')); ?>"></script>
    <script src="<?php echo e(asset('admin/app-assets/js/scripts/extensions/sweet-alerts.js')); ?>"></script>
    <?php echo $__env->make('provider.shared.filter_js', ['index_route' => route('provider.subscriptions.index')], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <script>

        function renewSubscription(subscriptionId) {
            Swal.fire({
                title: '<?php echo e(__("provider.confirm_renew_subscription")); ?>',
                text: '<?php echo e(__("provider.renew_subscription_description")); ?>',
                type: 'question',
                showCancelButton: true,
                confirmButtonColor: '#28a745',
                cancelButtonColor: '#6c757d',
                confirmButtonText: '<?php echo e(__("provider.yes_renew")); ?>',
                cancelButtonText: '<?php echo e(__("provider.cancel")); ?>',
                reverseButtons: true
            }).then((result) => {
                if (result.value) {
                    // Show loading
                    Swal.fire({
                        title: '<?php echo e(__("provider.processing")); ?>',
                        text: '<?php echo e(__("provider.please_wait")); ?>',
                        type: 'info',
                        allowOutsideClick: false,
                        allowEscapeKey: false,
                        showConfirmButton: false,
                        onOpen: () => {
                            Swal.showLoading();
                        }
                    });

                    $.ajax({
                        url: '<?php echo e(route("provider.subscriptions.renew", ":id")); ?>'.replace(':id', subscriptionId),
                        method: 'POST',
                        data: {
                            _token: '<?php echo e(csrf_token()); ?>'
                        },
                        success: function(response) {
                            if (response.status === 'redirect') {
                                // Redirect to payment page for renewal
                                Swal.fire({
                                    title: '<?php echo e(__("provider.redirecting_to_payment")); ?>',
                                    text: '<?php echo e(__("provider.please_wait")); ?>',
                                    type: 'info',
                                    timer: 2000,
                                    showConfirmButton: false
                                }).then(() => {
                                    window.location.href = response.payment_url;
                                });
                            } else if (response.status === 'success') {
                                Swal.fire({
                                    title: '<?php echo e(__("provider.success")); ?>',
                                    text: response.message,
                                    type: 'success',
                                    confirmButtonColor: '#28a745',
                                    confirmButtonText: '<?php echo e(__("provider.ok")); ?>'
                                }).then(() => {
                                    location.reload();
                                });
                            } else {
                                Swal.fire({
                                    title: '<?php echo e(__("provider.error")); ?>',
                                    text: response.message,
                                    type: 'error',
                                    confirmButtonColor: '#dc3545',
                                    confirmButtonText: '<?php echo e(__("provider.ok")); ?>'
                                });
                            }
                        },
                        error: function() {
                            Swal.fire({
                                title: '<?php echo e(__("provider.error")); ?>',
                                text: '<?php echo e(__("provider.error_occurred")); ?>',
                                type: 'error',
                                confirmButtonColor: '#dc3545',
                                confirmButtonText: '<?php echo e(__("provider.ok")); ?>'
                            });
                        }
                    });
                }
            });
        }

        function upgradeSubscription(subscriptionId) {
            Swal.fire({
                title: '<?php echo e(__("provider.confirm_upgrade_subscription")); ?>',
                text: '<?php echo e(__("provider.upgrade_subscription_description")); ?>',
                type: 'question',
                showCancelButton: true,
                confirmButtonColor: '#007bff',
                cancelButtonColor: '#6c757d',
                confirmButtonText: '<?php echo e(__("provider.yes_upgrade")); ?>',
                cancelButtonText: '<?php echo e(__("provider.cancel")); ?>',
                reverseButtons: true
            }).then((result) => {
                if (result.value) {
                    // Show loading
                    Swal.fire({
                        title: '<?php echo e(__("provider.redirecting_to_plans")); ?>',
                        text: '<?php echo e(__("provider.please_wait")); ?>',
                        type: 'info',
                        timer: 2000,
                        showConfirmButton: false
                    }).then(() => {
                        // Redirect to plans page for upgrade
                        window.location.href = '<?php echo e(route("provider.plans.index")); ?>?upgrade=' + subscriptionId;
                    });
                }
            });
        }

        function cancelSubscription(subscriptionId) {
            Swal.fire({
                title: '<?php echo e(__("provider.confirm_cancel_subscription")); ?>',
                text: '<?php echo e(__("provider.cancel_subscription_description")); ?>',
                type: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#dc3545',
                cancelButtonColor: '#6c757d',
                confirmButtonText: '<?php echo e(__("provider.yes_cancel")); ?>',
                cancelButtonText: '<?php echo e(__("provider.no_keep")); ?>',
                reverseButtons: true
            }).then((result) => {
                if (result.value) {
                    // Show loading
                    Swal.fire({
                        title: '<?php echo e(__("provider.processing")); ?>',
                        text: '<?php echo e(__("provider.please_wait")); ?>',
                        type: 'info',
                        allowOutsideClick: false,
                        allowEscapeKey: false,
                        showConfirmButton: false,
                        onOpen: () => {
                            Swal.showLoading();
                        }
                    });

                    $.ajax({
                        url: '<?php echo e(route("provider.subscriptions.cancel", ":id")); ?>'.replace(':id', subscriptionId),
                        method: 'POST',
                        data: {
                            _token: '<?php echo e(csrf_token()); ?>'
                        },
                        success: function(response) {
                            if (response.status === 'success') {
                                Swal.fire({
                                    title: '<?php echo e(__("provider.success")); ?>',
                                    text: response.message,
                                    type: 'success',
                                    confirmButtonColor: '#28a745',
                                    confirmButtonText: '<?php echo e(__("provider.ok")); ?>'
                                }).then(() => {
                                    location.reload();
                                });
                            } else {
                                Swal.fire({
                                    title: '<?php echo e(__("provider.error")); ?>',
                                    text: response.message,
                                    type: 'error',
                                    confirmButtonColor: '#dc3545',
                                    confirmButtonText: '<?php echo e(__("provider.ok")); ?>'
                                });
                            }
                        },
                        error: function() {
                            Swal.fire({
                                title: '<?php echo e(__("provider.error")); ?>',
                                text: '<?php echo e(__("provider.error_occurred")); ?>',
                                type: 'error',
                                confirmButtonColor: '#dc3545',
                                confirmButtonText: '<?php echo e(__("provider.ok")); ?>'
                            });
                        }
                    });
                }
            });
        }
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('provider.layout.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\Workstation\Taswk\elhafla\resources\views/provider/subscriptions/index.blade.php ENDPATH**/ ?>