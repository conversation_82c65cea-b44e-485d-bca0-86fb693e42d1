<?php

return [
    // Authentication
    'provider_login' => 'Provider Login',
    'provider_register' => 'Provider Registration',
    'provider_dashboard' => 'Provider Dashboard',
    'welcome_provider' => 'Welcome',
    'provider_login_subtitle' => 'Please sign in to your account to continue',
    'provider_register_subtitle' => 'Join us as a service provider and get more customers',
    'join_as_provider' => 'Join as Provider',
    'registered_successfully_choose_plan' => 'Registration successful! Please choose a subscription plan',
    'complete_provider_registration' => 'Complete Provider Registration',
    'complete_your_profile' => 'Complete Your Profile',
    'complete_registration_subtitle' => 'Please complete your business information to activate your account',
    'already_completed' => 'Registration already completed',
    'registration_completed' => 'Registration completed successfully',
    'complete_registration_first' => 'Please complete registration first',
    'under_review' => 'Under Review',
    'blocked' => 'Blocked',
    'not_active' => 'Not Active',
    'unauthorized' => 'Unauthorized',

    // Dashboard
    'welcome_back' => 'Welcome Back',
    'dashboard_subtitle' => 'Manage your business efficiently through your dashboard',
    'main_page' => 'Main Page',
    'account_status' => 'Account Status',
    'active' => 'Active',
    'inactive' => 'Inactive',
    'pending' => 'Pending',
    'account_active_desc' => 'Your account is active and you can receive orders',
    'account_pending_desc' => 'Your account is under review, it will be activated soon',
    'quick_actions' => 'Quick Actions',
    'recent_activities' => 'Recent Activities',
    'no_recent_activities' => 'No recent activities',

    // Statistics
    'total_services' => 'Total Services',
    'total_orders' => 'Total Orders',
    'total_reviews' => 'Total Reviews',
    'average_rating' => 'Average Rating',
    'pending_orders' => 'Pending Orders',
    'completed_orders' => 'Completed Orders',
    'monthly_revenue' => 'Monthly Revenue',
    'active_subscriptions' => 'Active Subscriptions',
    'orders_overview' => 'Orders Overview',
    'orders' => 'Orders',
    'statistics' => 'Statistics',

    // Profile
    'profile' => 'Profile',
    'personal_information' => 'Personal Information',
    'business_information' => 'Business Information',
    'edit_profile' => 'Edit Profile',
    'name' => 'Name',
    'email' => 'Email',
    'phone' => 'Phone',
    'gender' => 'Gender',
    'male' => 'Male',
    'female' => 'Female',
    'not_specified' => 'Not Specified',
    'member_since' => 'Member Since',
    'business_name' => 'Business Name',
    'business_email' => 'Business Email',
    'business_phone' => 'Business Phone',
    'business_description' => 'Business Description',
    'business_logo' => 'Business Logo',
    'category' => 'Category',
    'city' => 'City',
    'region' => 'Region',
    'no_business_info' => 'No business information entered yet',
    'complete_registration' => 'Complete Registration',
    'days_active' => 'Days Active',
    'save_changes' => 'Save Changes',
    'cancel' => 'Cancel',
    'change_password' => 'Change Password',
    'current_password' => 'Current Password',
    'new_password' => 'New Password',
    'confirm_new_password' => 'Confirm New Password',
    'profile_updated_successfully' => 'Profile updated successfully',
    'current_password_incorrect' => 'Current password is incorrect',
    'password_updated_successfully' => 'Password updated successfully',
    'saving' => 'Saving...',
    'changing' => 'Changing...',
    'error_occurred' => 'An error occurred, please try again',
    'country_code' => 'Country Code',
    'latitude' => 'Latitude',
    'longitude' => 'Longitude',
    'logo_requirements' => 'File must be an image (jpeg, png, jpg, gif, svg) and less than 2MB',
    'select_gender' => 'Select Gender',
    'select_category' => 'Select Category',
    'select_city' => 'Select City',
    'select_region' => 'Select Region',
    'incomplete_profile' => 'Incomplete Profile',

    // Services
    'services' => 'Services',
    'my_services' => 'My Services',
    'add_service' => 'Add Service',
    'add_new_service' => 'Add New Service',
    'service_name' => 'Service Name',
    'service_description' => 'Service Description',
    'service_price' => 'Service Price',
    'service_name_ar' => 'Service Name (Arabic)',
    'service_name_en' => 'Service Name (English)',
    'service_description_ar' => 'Service Description (Arabic)',
    'service_description_en' => 'Service Description (English)',
    'enter_service_name_ar' => 'Enter service name in Arabic',
    'enter_service_name_en' => 'Enter service name in English',
    'enter_service_description_ar' => 'Enter service description in Arabic',
    'enter_service_description_en' => 'Enter service description in English',
    'arabic' => 'Arabic',
    'english' => 'English',
    'service_information' => 'Service Information',
    'service_features' => 'Service Features',
    'service_features_note' => 'You can add more features later',
    'save_service' => 'Save Service',
    'back_to_services' => 'Back to Services',
    'search_services' => 'Search Services',
    'all_statuses' => 'All Statuses',
    'filter' => 'Filter',
    'delete_selected' => 'Delete Selected',
    'loading' => 'Loading...',
    'no_services_found' => 'No services found',
    'no_services_desc' => 'You haven\'t added any services yet',
    'add_first_service' => 'Add First Service',
    'view' => 'View',
    'edit' => 'Edit',
    'delete' => 'Delete',
    'activate' => 'Activate',
    'deactivate' => 'Deactivate',
    'confirm_delete_service' => 'Are you sure you want to delete this service?',
    'confirm_delete_services' => 'Are you sure you want to delete selected services?',
    'service_created_successfully' => 'Service created successfully',
    'service_updated_successfully' => 'Service updated successfully',
    'service_deleted_successfully' => 'Service deleted successfully',
    'services_deleted_successfully' => 'Services deleted successfully',
    'service_status_updated' => 'Service status updated',
    'no_items_selected' => 'No items selected',
    'edit_service' => 'Edit Service',
    'view_service' => 'View Service',
    'update_service' => 'Update Service',
    'updating' => 'Updating...',
    'service_details' => 'Service Details',
    'not_provided' => 'Not Provided',
    'created_at' => 'Created At',
    'updated_at' => 'Updated At',
    'service_id' => 'Service ID',
    'provider' => 'Provider',
    'service_statistics' => 'Service Statistics',
    'reviews' => 'Reviews',
    'statistics_coming_soon' => 'Statistics Coming Soon',
    'delete_service' => 'Delete Service',
    'currency' => 'SAR',
    'description' => 'Description',
    'price' => 'Price',
    'status' => 'Status',
    'actions' => 'Actions',

    // Plans & Subscriptions
    'choose_your_plan' => 'Choose Your Plan',
    'welcome_choose_plan' => 'Welcome! Choose the right plan for you',
    'choose_plan_subtitle' => 'Choose the plan that suits your needs and start growing your business',
    'you_have_active_subscription' => 'You already have an active subscription',
    'most_popular' => 'Most Popular',
    'discount' => 'Discount',
    'save' => 'Save',
    'sar' => 'SAR',
    'days' => 'Days',
    'plan_name' => 'Plan Name',
    'unlimited_services' => 'Unlimited Services',
    'customer_support' => 'Customer Support',
    'analytics_dashboard' => 'Analytics Dashboard',
    'mobile_app_access' => 'Mobile App Access',
    'priority_listing' => 'Priority Listing',
    'marketing_tools' => 'Marketing Tools',
    'select_this_plan' => 'Select This Plan',
    'current_plan' => 'Current Plan',
    'choose_payment_method' => 'Choose Payment Method',
    'proceed_to_payment' => 'Proceed to Payment',
    'skip_for_now_text' => 'You can skip this step and subscribe later',
    'skip_and_complete_profile' => 'Skip and Complete Profile',
    'please_select_plan_and_payment' => 'Please select plan and payment method',
    'processing' => 'Processing...',
    'plan_selected_successfully' => 'Plan selected successfully',
    'payment_creation_failed' => 'Payment creation failed',
    'payment_error_occurred' => 'Payment error occurred',
    'invalid_payment_response' => 'Invalid payment response',
    'subscription_not_found' => 'Subscription not found',
    'payment_successful' => 'Payment Successful',
    'payment_failed' => 'Payment Failed',
    'payment_verification_failed' => 'Payment verification failed',

    // Payment Success
    'payment_success_message' => 'Payment successful and your subscription has been activated.',
    'subscription_details' => 'Subscription Details',
    'plan_name' => 'Plan Name',
    'duration' => 'Duration',
    'amount_paid' => 'Amount Paid',
    'subscription_starts' => 'Subscription Starts',
    'subscription_expires' => 'Subscription Expires',
    'what_next' => 'What\'s Next?',
    'what_next_description' => 'You can now complete your profile or go directly to the dashboard',
    'complete_profile' => 'Complete Profile',
    'go_to_dashboard' => 'Go to Dashboard',
    'payment_confirmation_email' => 'Payment confirmation will be sent to your email',

    // Payment Error
    'payment_failed_message' => 'Sorry, the payment was not successful. Please try again or contact support',
    'error_details' => 'Error Details',
    'common_payment_issues' => 'Common Payment Issues',
    'insufficient_funds' => 'Insufficient funds',
    'card_expired' => 'Card expired',
    'incorrect_card_details' => 'Incorrect card details',
    'bank_declined' => 'Bank declined',
    'network_timeout' => 'Network timeout',
    'what_can_you_do' => 'What can you do?',
    'try_again' => 'Try Again',
    'contact_support' => 'Contact Support',
    'call_support' => 'Call Support',
    'alternative_options' => 'Alternative Options',
    'different_card' => 'Different Card',
    'try_different_payment_method' => 'Try a different payment method',
    'try_later' => 'Try Later',
    'temporary_issue_try_later' => 'May be a temporary issue, try later',
    'contact_bank' => 'Contact Bank',
    'contact_bank_for_help' => 'Contact your bank for help',
    'payment_error_reference' => 'Error Reference',
    'error_time' => 'Error Time',
    'auto_redirect_in' => 'Auto redirect in',
    'seconds' => 'seconds',

    // Subscriptions
    'subscriptions' => 'Subscriptions',
    'current_subscription' => 'Current Subscription',
    'plan' => 'Plan',
    'expires_on' => 'Expires On',
    'expires_soon' => 'Expires Soon',
    'days_remaining' => 'Days Remaining',
    'renew' => 'Renew',
    'upgrade' => 'Upgrade',
    'no_active_subscription' => 'No Active Subscription',
    'no_active_subscription_desc' => 'You don\'t have an active subscription currently. Subscribe now to enjoy all features',
    'subscribe_now' => 'Subscribe Now',
    'total_subscriptions' => 'Total Subscriptions',
    'expired_subscriptions' => 'Expired Subscriptions',
    'pending_subscriptions' => 'Pending Subscriptions',
    'subscription_history' => 'Subscription History',
    'new_subscription' => 'New Subscription',
    'all_payment_statuses' => 'All Payment Statuses',
    'paid' => 'Paid',
    'cancelled' => 'Cancelled',
    'not_set' => 'Not Set',
    'no_subscriptions_found' => 'No subscriptions found',
    'no_subscriptions_desc' => 'You haven\'t made any subscriptions yet',
    'confirm_renew_subscription' => 'Renew Subscription',
    'renew_subscription_description' => 'This will create a new subscription with the same plan. You will be redirected to payment if required.',
    'yes_renew' => 'Yes, Renew',
    'confirm_upgrade_subscription' => 'Upgrade Subscription',
    'upgrade_subscription_description' => 'You will be redirected to the plans page to choose a new plan.',
    'yes_upgrade' => 'Yes, Upgrade',
    'confirm_cancel_subscription' => 'Cancel Subscription',
    'cancel_subscription_description' => 'This action cannot be undone. Your subscription will be cancelled immediately.',
    'yes_cancel' => 'Yes, Cancel',
    'no_keep' => 'No, Keep It',
    'processing' => 'Processing',
    'redirecting_to_payment' => 'Redirecting to Payment',
    'redirecting_to_plans' => 'Redirecting to Plans',
    'success' => 'Success',
    'error' => 'Error',
    'ok' => 'OK',
    'payment_method' => 'Payment Method',
    'payment_status' => 'Payment Status',
    'expiry_date' => 'Expiry Date',

    // Common
    'back' => 'Back',
    'next' => 'Next',
    'previous' => 'Previous',
    'submit' => 'Submit',
    'search' => 'Search',
    'logout' => 'Logout',
    'settings' => 'Settings',
    'account' => 'Account',
    'copyrights' => 'Copyright',
    'all_rights_reserved' => 'All Rights Reserved',
    'dashboard_version' => 'Dashboard Version',

    // Subscription Requirements
    'subscription_required' => 'Active subscription required',
    'subscription_required_to_access' => 'You need an active subscription to access this feature',
    'subscription_id' => 'Subscription ID',
    'payment_reference' => 'Payment Reference',
    'payment_details' => 'Payment Details',
    'subscription_renewed_successfully' => 'Subscription renewed successfully',
    'subscription_upgraded_successfully' => 'Subscription upgraded successfully',
    'subscription_cancelled_successfully' => 'Subscription cancelled successfully',
    'upgrading_from_plan' => 'Upgrading from plan',
    'payment_initialization_failed' => 'Payment initialization failed',

    // Registration Form
    'account_details' => 'Account Details',
    'enter_account_details' => 'Enter your account details',
    'phone_verification' => 'Phone Verification',
    'verify_phone_number' => 'Verify your phone number',
    'personal_info' => 'Personal Information',
    'add_personal_info' => 'Add your personal information',
    'name_required' => 'Name is required',
    'email_required' => 'Email is required',
    'email_invalid' => 'Email is invalid',
    'country_code_required' => 'Country code is required',
    'phone_required' => 'Phone number is required',
    'phone_invalid' => 'Phone number is invalid (must be 9 digits)',
    'password_required' => 'Password is required',
    'password_min_length' => 'Password must be at least 8 characters',
    'password_confirmation_mismatch' => 'Password confirmation does not match',
    'terms_required' => 'You must agree to terms and conditions',
    'gender_required' => 'Gender is required',
    'agree_to' => 'I agree to',
    'terms_conditions' => 'Terms & Conditions',
    'verification_code_sent_to' => 'Verification code sent to',
    'enter_verification_code' => 'Enter verification code',
    'didnt_receive_code' => 'Didn\'t receive the code?',
    'resend_code' => 'Resend Code',
    'verify' => 'Verify',
    'verifying' => 'Verifying',
    'sending' => 'Sending',
    'registering' => 'Registering',
    'complete_registration_btn' => 'Complete Registration',
    'registration_complete_message' => 'After completing registration, you can choose a subscription plan and complete your business profile.',
    'already_have_account_text' => 'Already have an account?',
    'login_now_link' => 'Login now',
    'verification_code_sent' => 'Verification code sent successfully',
    'verification_code_verified' => 'Code verified successfully',
    'verification_code_invalid' => 'Invalid verification code',
    'verification_code_expired' => 'Verification code expired',
    'phone_already_verified' => 'Phone number already verified',
    'phone_already_exists' => 'Phone number already registered',

    // Forget Password
    'forget_password' => 'Forgot Password',
    'forget_password_subtitle' => 'Enter your phone number to reset password',
    'enter_phone_number' => 'Enter phone number',
    'send_reset_code' => 'Send Reset Code',
    'reset_password' => 'Reset Password',
    'new_password_reset' => 'New Password',
    'confirm_new_password_reset' => 'Confirm New Password',
    'reset_password_success' => 'Password reset successfully',
    'reset_code_sent' => 'Reset code sent to your phone',
    'reset_code_invalid' => 'Invalid reset code',
    'phone_not_found' => 'Phone number not registered',
    'phone_verification_required' => 'Phone verification is required first',
    'remember_password' => 'Remember password',
    'verification_code_hint' => 'Test verification code',

    // Plan Selection
    'confirm_payment' => 'Confirm Payment',
    'confirm_plan_selection' => 'Confirm Plan Selection',
    'confirm_free_plan_selection' => 'Confirm Free Plan Selection',
    'proceed_to_payment_confirmation' => 'Do you want to proceed to payment?',
    'please_select_plan' => 'Please select a plan',
    'payment_method_not_available' => 'Payment method not available',
    'processing_payment' => 'Processing payment...',

    // Business Registration
    'business_name_required' => 'Business name is required',
    'category_required' => 'Category is required',
    'city_required' => 'City is required',
    'region_required' => 'Region is required',
    'location_required' => 'Location is required',
    'business_location' => 'Business Location',
    'click_map_to_select_location' => 'Click on the map to select location',
    'click_to_access' => 'Click to access',
    'available' => 'Available',
    'view_orders' => 'View Orders',
    'manage_working_hours' => 'Manage Working Hours',
    'view_statistics' => 'View Statistics',
    'chat' => 'Chat',
    'conversations' => 'Conversations',
    'select_conversation' => 'Select a conversation to start chatting',
    'type_message' => 'Type your message...',
    'is_typing' => 'is typing',
    'loading' => 'Loading...',
    'error_loading_conversations' => 'Error loading conversations',
    'no_conversations' => 'No conversations yet',
    'connected' => 'Connected',
    'disconnected' => 'Disconnected',
    'connecting' => 'Connecting...',
    'connection_error' => 'Connection Error',

    // Table and actions
    'control' => 'Control',
    'show' => 'Show',
    'confirm' => 'Confirm',
    'are_you_sure' => 'Are you sure?',
    'confirm_delete_selected' => 'Are you sure you want to delete the selected items?',
    'confirm_delete_item' => 'Are you sure you want to delete this item?',
    'the_selected_has_been_successfully_deleted' => 'The selected items have been successfully deleted',
    'there_are_no_matches_matching' => 'No matching results found',
    'sort_by' => 'Sort by',
    'choose' => 'Choose',
    'progressive' => 'Ascending',
    'descending' => 'Descending',
    'beginning_date' => 'Start Date',
    'end_date' => 'End Date',
    'enter_service_name' => 'Enter service name',

    // Payment Success Updates
    'under_review_title' => 'Your Request is Under Review',
    'under_review_message' => 'Your information will be reviewed and your account will be approved within 24-48 hours.',
    'what_happens_next' => 'What happens next?',
    'step_review' => 'Review',
    'step_review_desc' => 'Your data and documents will be reviewed',
    'step_approval' => 'Approval',
    'step_approval_desc' => 'You will receive approval notification',
    'step_start' => 'Start',
    'step_start_desc' => 'You can start using the platform',
    'contact_support_if_needed' => 'If you have any questions, please contact technical support',
    'go_to_login' => 'Go to Login Page',
    'registration_date' => 'Registration Date',

    // Login Status Messages
    'account_under_review' => 'Your account is under review. You will be notified when your account is approved.',
    'choose_subscription_plan' => 'Please choose a subscription plan to continue using the platform.',

    // Free Subscription Messages
    'free_subscription_created' => 'Free subscription created successfully! You can now access the dashboard.',
    'subscription_creation_failed' => 'Failed to create subscription. Please try again.',
    'free' => 'Free',
    'activate_free_plan' => 'Activate Free Plan',
    'confirm_free_plan' => 'Confirm Free Plan',
    'confirm_free_plan_activation' => 'Do you want to activate this free plan?',
    'yes_activate' => 'Yes, Activate',
    'activating_free_plan' => 'Activating free plan...',

    // Working Hours
    'working_hours' => 'Working Hours',
    'add_working_hour' => 'Add Working Hour',
    'edit_working_hour' => 'Edit Working Hour',
    'delete_working_hour' => 'Delete Working Hour',
    'day' => 'Day',
    'start_time' => 'Start Time',
    'end_time' => 'End Time',
    'is_worker' => 'Active',
    'actions' => 'Actions',
    'yes' => 'Yes',
    'confirm_password' => 'password confirmation',
    'password' => 'password',
    'no' => 'No',
    'working_hours_list' => 'Working Hours List',
    'no_working_hours_found' => 'No working hours found.',
    'working_hours_created_successfully' => 'Working hour created successfully',
    'working_hours_updated_successfully' => 'Working hour updated successfully',
    'working_hours_deleted_successfully' => 'Working hour deleted successfully',
    'are_you_sure' => 'Are you sure?',
    'delete_working_hour_confirmation' => 'This working hour will be permanently deleted. This action cannot be undone.',
    'yes_delete' => 'Yes, Delete',
    'cancel' => 'Cancel',
    'deleting' => 'Deleting...',
    'please_wait' => 'Please wait while we process your request.',
    'success' => 'Success',
    'error' => 'Error',
    'ok' => 'OK',
    'validation_error' => 'Validation Error',
    'creating' => 'Creating...',
    'updating' => 'Updating...',
    'saturday' => 'Saturday',
    'sunday' => 'Sunday',
    'monday' => 'Monday',
    'tuesday' => 'Tuesday',
    'wednesday' => 'Wednesday',
    'thursday' => 'Thursday',
    'friday' => 'Friday',

    // Service Image
    'service_image' => 'Service Image',
    'upload_service_image' => 'Upload Service Image',

    // Notifications
    'notifications' => 'Notifications',
    'new_notification' => 'New Notification',
    'notification_content' => 'Notification Content',
    'no_notifications' => 'No Notifications',
    'no_notifications_desc' => 'Your notifications will appear here when they arrive',
    'mark_all_read' => 'Mark All as Read',
    'mark_as_read' => 'Mark as Read',
    'delete_all' => 'Delete All',
    'view_all' => 'View All',
    'new' => 'New',
    'loading' => 'Loading',
    'error_loading_notifications' => 'Error loading notifications',
    'notifications_marked_read' => 'All notifications marked as read',
    'notification_marked_read' => 'Notification marked as read',
    'notification_not_found' => 'Notification not found',
    'notification_deleted' => 'Notification deleted',
    'all_notifications_deleted' => 'All notifications deleted',
    'are_you_sure' => 'Are you sure?',
    'delete_notification_confirm' => 'This notification will be permanently deleted',
    'delete_all_notifications_confirm' => 'All notifications will be permanently deleted',
    'yes_delete' => 'Yes, Delete',
    'yes_delete_all' => 'Yes, Delete All',
    'cancel' => 'Cancel',
    'error_occurred' => 'An error occurred',
];
