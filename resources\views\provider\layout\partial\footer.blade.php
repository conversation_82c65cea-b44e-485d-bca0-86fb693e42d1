
<div class="sidenav-overlay"></div>
<div class="drag-target"></div>

<footer class="footer footer-static footer-light">
<p class="clearfix blue-grey lighten-2 mb-0">
    <span class="float-md-left d-block d-md-inline-block mt-25">
        {{ __('admin.Copyrights') }} &copy; {{ \Carbon\Carbon::now()->year }}
        <a class="text-bold-800 grey darken-2" href="https://tasawk.com.sa/" target="_blank">{{  __('site.Tasawk')}}</a>
        {{ __('admin.all_rights_reserved') }}
    </span>
    <span class="float-md-right d-none d-md-block">
        <a href="https://tasawk.com.sa/" rel="follow" target="_blank"> {{  __('site.Tasawk')}} </a>
        <a href="mailto:<EMAIL>"><i class="feather icon-mail pink"></i></a>
        <a href="tel:+966540664139"><i class="feather icon-phone pink"></i></a>
    </span>
</p>
</footer>

<script src="https://cdnjs.cloudflare.com/ajax/libs/switchery/0.8.2/switchery.min.js"></script>

<script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>


<script src="{{asset('admin/active.js')}}"></script>
<script src="{{asset('admin/assets/js/flatpickr.js')}}"></script>
<script src="{{asset('admin/app-assets/vendors/js/vendors.min.js')}}"></script>
<script src="{{asset('admin/app-assets/js/core/app-menu.js')}}"></script>
<script src="{{asset('admin/app-assets/js/core/app.js')}}"></script>
<script src="{{asset('admin/app-assets/js/scripts/components.js')}}"></script>
<script src="{{asset('admin/app-assets/vendors/js/extensions/toastr.min.js')}}"></script>
<script src="//code.jquery.com/ui/1.11.4/jquery-ui.js"></script>
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

<script>
    // input date js
    var $list = $(":input[type='date']");
    $(window).on('load', function () {
        if ($($list).length > 0) {
            $(document).find($list).addClass("custom-input-date");
            $(document).find($list).parents(".controls").addClass("parent-input-date");
            $($list).prop("type", "text");
            flatpickr($list, {
                disableMobile: true,
                // minDate: "today",
            });
        }
    })
    $(document).ready(function () {
        $(".select2").select2();
    });

    // Language Selector Functionality
    $(document).ready(function() {
        $('#languageSelector').on('click', function(e) {
            e.preventDefault();

            var currentLang = '{{ app()->getLocale() }}';
            var newLang = currentLang === 'ar' ? 'en' : 'ar';

            // Show loading state
            $(this).css('opacity', '0.6');

            // Redirect to change language
            window.location.href = '{{ url("provider/lang") }}/' + newLang;
        });
    });

    // Notifications Dropdown Functionality
    $(document).ready(function() {
        let notificationDropdown = $('.dropdown-notification');
        let notificationsList = $('#notifications-list');
        let notificationCount = $('#notification-count');
        let notificationCountHeader = $('.notification-count-header');

        // Load notifications when dropdown is opened
        notificationDropdown.on('show.bs.dropdown', function() {
            loadNotifications();
        });

        // Load notifications function
        function loadNotifications() {
            notificationsList.html('<div class="text-center p-2"><i class="feather icon-loader spin"></i> {{__("provider.loading")}}...</div>');

            $.ajax({
                url: '{{ route("provider.notifications.get") }}',
                method: 'GET',
                success: function(response) {
                    updateNotificationCount(response.unread_count);
                    renderNotifications(response.notifications);
                },
                error: function() {
                    notificationsList.html('<div class="text-center p-2 text-danger">{{__("provider.error_loading_notifications")}}</div>');
                }
            });
        }

        // Update notification count
        function updateNotificationCount(count) {
            if (count > 0) {
                notificationCount.text(count).show().addClass('pulse');
                notificationCountHeader.text(count + ' {{__("provider.new")}}');
                // Remove pulse animation after 3 seconds
                setTimeout(function() {
                    notificationCount.removeClass('pulse');
                }, 3000);
            } else {
                notificationCount.hide().removeClass('pulse');
                notificationCountHeader.text('0 {{__("provider.new")}}');
            }
        }

        // Render notifications
        function renderNotifications(notifications) {
            if (notifications.length === 0) {
                notificationsList.html('<div class="text-center p-3"><i class="feather icon-bell-off"></i><br>{{__("provider.no_notifications")}}</div>');
                return;
            }

            let html = '';
            notifications.forEach(function(notification) {
                const clickUrl = '{{ route("provider.notifications.click", ":id") }}'.replace(':id', notification.id);
                html += `
                    <a class="d-flex notification-item" href="${clickUrl}" data-id="${notification.id}">
                        <div class="media d-flex align-items-start">
                            <div class="media-left">
                                <div class="avatar bg-light-primary">
                                    <div class="avatar-content">
                                        <i class="feather icon-bell font-medium-3"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="media-body">
                                <p class="media-heading"><span class="font-weight-bolder">${notification.title}</span></p>
                                <small class="notification-text">${notification.body}</small>
                                <small class="text-muted d-block">${notification.created_at}</small>
                            </div>
                        </div>
                    </a>
                `;
            });
            notificationsList.html(html);
        }

        // Notification items will navigate directly via href
        // No need to prevent default or handle click separately

        // Mark all as read
        $('#mark-all-read').on('click', function(e) {
            e.preventDefault();
            markAllAsRead();
        });

        // Mark single notification as read
        function markAsRead(id) {
            $.ajax({
                url: '{{ route("provider.notifications.mark-read", ":id") }}'.replace(':id', id),
                method: 'POST',
                data: {
                    _token: '{{ csrf_token() }}'
                },
                success: function(response) {
                    if (response.success) {
                        loadNotifications();
                        toastr.success(response.message);
                    }
                }
            });
        }

        // Mark all notifications as read
        function markAllAsRead() {
            $.ajax({
                url: '{{ route("provider.notifications.mark-all-read") }}',
                method: 'POST',
                data: {
                    _token: '{{ csrf_token() }}'
                },
                success: function(response) {
                    if (response.success) {
                        loadNotifications();
                        updateNotificationCount(0);
                        toastr.success(response.message);
                    }
                }
            });
        }

        // Load initial notification count
        loadNotifications();

        // Update notification count immediately on page load
        updateNotificationCount({{ auth('provider')->user()->unreadNotifications()->count() }});

        // Refresh notification count every 30 seconds
        setInterval(function() {
            refreshNotificationCountFromServer();
        }, 30000);
    });

    // Function to refresh notification count from server
    function refreshNotificationCountFromServer() {
        $.ajax({
            url: '{{ route("provider.notifications.get") }}',
            method: 'GET',
            success: function(response) {
                updateNotificationCount(response.unread_count);
            },
            error: function() {
                console.log('Failed to refresh notification count');
            }
        });
    }
</script>

@yield('js')

<x-admin.alert />
{{-- <x-socket /> --}}
</body>
</html>