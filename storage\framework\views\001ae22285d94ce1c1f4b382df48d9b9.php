
<div class="sidenav-overlay"></div>
<div class="drag-target"></div>

<footer class="footer footer-static footer-light">
<p class="clearfix blue-grey lighten-2 mb-0">
    <span class="float-md-left d-block d-md-inline-block mt-25">
        <?php echo e(__('admin.Copyrights')); ?> &copy; <?php echo e(\Carbon\Carbon::now()->year); ?>

        <a class="text-bold-800 grey darken-2" href="https://tasawk.com.sa/" target="_blank"><?php echo e(__('site.Tasawk')); ?></a>
        <?php echo e(__('admin.all_rights_reserved')); ?>

    </span>
    <span class="float-md-right d-none d-md-block">
        <a href="https://tasawk.com.sa/" rel="follow" target="_blank"> <?php echo e(__('site.Tasawk')); ?> </a>
        <a href="mailto:<EMAIL>"><i class="feather icon-mail pink"></i></a>
        <a href="tel:+966540664139"><i class="feather icon-phone pink"></i></a>
    </span>
</p>
</footer>

<script src="https://cdnjs.cloudflare.com/ajax/libs/switchery/0.8.2/switchery.min.js"></script>

<script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>


<script src="<?php echo e(asset('admin/active.js')); ?>"></script>
<script src="<?php echo e(asset('admin/assets/js/flatpickr.js')); ?>"></script>
<script src="<?php echo e(asset('admin/app-assets/vendors/js/vendors.min.js')); ?>"></script>
<script src="<?php echo e(asset('admin/app-assets/js/core/app-menu.js')); ?>"></script>
<script src="<?php echo e(asset('admin/app-assets/js/core/app.js')); ?>"></script>
<script src="<?php echo e(asset('admin/app-assets/js/scripts/components.js')); ?>"></script>
<script src="<?php echo e(asset('admin/app-assets/vendors/js/extensions/toastr.min.js')); ?>"></script>
<script src="//code.jquery.com/ui/1.11.4/jquery-ui.js"></script>
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

<script>
    // input date js
    var $list = $(":input[type='date']");
    $(window).on('load', function () {
        if ($($list).length > 0) {
            $(document).find($list).addClass("custom-input-date");
            $(document).find($list).parents(".controls").addClass("parent-input-date");
            $($list).prop("type", "text");
            flatpickr($list, {
                disableMobile: true,
                // minDate: "today",
            });
        }
    })
    $(document).ready(function () {
        $(".select2").select2();
    });

    // Language Selector Functionality
    $(document).ready(function() {
        $('#languageSelector').on('click', function(e) {
            e.preventDefault();

            var currentLang = '<?php echo e(app()->getLocale()); ?>';
            var newLang = currentLang === 'ar' ? 'en' : 'ar';

            // Show loading state
            $(this).css('opacity', '0.6');

            // Redirect to change language
            window.location.href = '<?php echo e(url("provider/lang")); ?>/' + newLang;
        });
    });

    // Notifications Dropdown Functionality
    $(document).ready(function() {
        let notificationDropdown = $('.dropdown-notification');
        let notificationsList = $('#notifications-list');
        let notificationCount = $('#notification-count');
        let notificationCountHeader = $('.notification-count-header');

        // Load notifications when dropdown is opened
        notificationDropdown.on('show.bs.dropdown', function() {
            loadNotifications();
        });

        // Load notifications function
        function loadNotifications() {
            notificationsList.html('<div class="text-center p-2"><i class="feather icon-loader spin"></i> <?php echo e(__("provider.loading")); ?>...</div>');

            $.ajax({
                url: '<?php echo e(route("provider.notifications.get")); ?>',
                method: 'GET',
                success: function(response) {
                    updateNotificationCount(response.unread_count);
                    renderNotifications(response.notifications);
                },
                error: function() {
                    notificationsList.html('<div class="text-center p-2 text-danger"><?php echo e(__("provider.error_loading_notifications")); ?></div>');
                }
            });
        }

        // Update notification count
        function updateNotificationCount(count) {
            if (count > 0) {
                notificationCount.text(count).show().addClass('pulse');
                notificationCountHeader.text(count + ' <?php echo e(__("provider.new")); ?>');
                // Remove pulse animation after 3 seconds
                setTimeout(function() {
                    notificationCount.removeClass('pulse');
                }, 3000);
            } else {
                notificationCount.hide().removeClass('pulse');
                notificationCountHeader.text('0 <?php echo e(__("provider.new")); ?>');
            }
        }

        // Render notifications
        function renderNotifications(notifications) {
            if (notifications.length === 0) {
                notificationsList.html('<div class="text-center p-3"><i class="feather icon-bell-off"></i><br><?php echo e(__("provider.no_notifications")); ?></div>');
                return;
            }

            let html = '';
            notifications.forEach(function(notification) {
                const clickUrl = '<?php echo e(route("provider.notifications.click", ":id")); ?>'.replace(':id', notification.id);
                html += `
                    <a class="d-flex notification-item" href="${clickUrl}" data-id="${notification.id}">
                        <div class="media d-flex align-items-start">
                            <div class="media-left">
                                <div class="avatar bg-light-primary">
                                    <div class="avatar-content">
                                        <i class="feather icon-bell font-medium-3"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="media-body">
                                <p class="media-heading"><span class="font-weight-bolder">${notification.title}</span></p>
                                <small class="notification-text">${notification.body}</small>
                                <small class="text-muted d-block">${notification.created_at}</small>
                            </div>
                        </div>
                    </a>
                `;
            });
            notificationsList.html(html);
        }

        // Notification items will navigate directly via href
        // No need to prevent default or handle click separately

        // Mark all as read
        $('#mark-all-read').on('click', function(e) {
            e.preventDefault();
            markAllAsRead();
        });

        // Mark single notification as read
        function markAsRead(id) {
            $.ajax({
                url: '<?php echo e(route("provider.notifications.mark-read", ":id")); ?>'.replace(':id', id),
                method: 'POST',
                data: {
                    _token: '<?php echo e(csrf_token()); ?>'
                },
                success: function(response) {
                    if (response.success) {
                        loadNotifications();
                        toastr.success(response.message);
                    }
                }
            });
        }

        // Mark all notifications as read
        function markAllAsRead() {
            $.ajax({
                url: '<?php echo e(route("provider.notifications.mark-all-read")); ?>',
                method: 'POST',
                data: {
                    _token: '<?php echo e(csrf_token()); ?>'
                },
                success: function(response) {
                    if (response.success) {
                        loadNotifications();
                        updateNotificationCount(0);
                        toastr.success(response.message);
                    }
                }
            });
        }

        // Load initial notification count
        loadNotifications();

        // Update notification count immediately on page load
        updateNotificationCount(<?php echo e(auth('provider')->user()->unreadNotifications()->count()); ?>);

        // Refresh notification count every 30 seconds
        setInterval(function() {
            refreshNotificationCountFromServer();
        }, 30000);
    });

    // Function to refresh notification count from server
    function refreshNotificationCountFromServer() {
        $.ajax({
            url: '<?php echo e(route("provider.notifications.get")); ?>',
            method: 'GET',
            success: function(response) {
                updateNotificationCount(response.unread_count);
            },
            error: function() {
                console.log('Failed to refresh notification count');
            }
        });
    }
</script>

<?php echo $__env->yieldContent('js'); ?>

<?php if (isset($component)) { $__componentOriginal07ddb047ae3451301e07dda88a33c35d = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal07ddb047ae3451301e07dda88a33c35d = $attributes; } ?>
<?php $component = App\View\Components\Admin\Alert::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('admin.alert'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\Admin\Alert::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal07ddb047ae3451301e07dda88a33c35d)): ?>
<?php $attributes = $__attributesOriginal07ddb047ae3451301e07dda88a33c35d; ?>
<?php unset($__attributesOriginal07ddb047ae3451301e07dda88a33c35d); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal07ddb047ae3451301e07dda88a33c35d)): ?>
<?php $component = $__componentOriginal07ddb047ae3451301e07dda88a33c35d; ?>
<?php unset($__componentOriginal07ddb047ae3451301e07dda88a33c35d); ?>
<?php endif; ?>

</body>
</html><?php /**PATH D:\Workstation\Taswk\elhafla\resources\views/provider/layout/partial/footer.blade.php ENDPATH**/ ?>