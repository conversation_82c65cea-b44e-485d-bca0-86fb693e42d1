<nav class="header-navbar navbar-expand-lg navbar navbar-with-menu floating-nav navbar-light navbar-shadow">
    <div class="navbar-wrapper">
        <div class="navbar-container content">
            <div class="navbar-collapse <?php if(app()->getLocale() == 'ar'): ?> d-flex justify-content-between <?php endif; ?>" id="navbar-mobile">

                
                <ul class="nav navbar-nav">
                    <li class="nav-item mobile-menu d-xl-none <?php if(app()->getLocale() == 'ar'): ?> <?php else: ?> mr-auto <?php endif; ?>">
                        <a class="nav-link nav-menu-main menu-toggle hidden-xs" href="#">
                            <i class="ficon feather icon-menu"></i>
                        </a>
                    </li>
                </ul>

                
                <div class="breadcrumb-wrapper d-flex align-items-center <?php if(app()->getLocale() == 'ar'): ?> order-2 <?php else: ?> mr-auto float-left <?php endif; ?>">
                    <div class="breadcrumb-text" style="font-size:14px; font-family:'cairo'; color:black;">
                        <a href="<?php echo e(url('provider/dashboard')); ?>" style="color:black;">
                            <span class="user-name">
                                <i class="feather icon-home"></i> <?php echo e(__('site.home')); ?>

                            </span>
                        </a>
                        <?php if(Route::currentRouteName() != 'provider.dashboard'): ?>
                            <span class="user-name hint-slash">/</span>
                            <a href="javascript:void(0)" style="color:black;">
                                <span class="user-name"><?php echo e(__('provider.'.\Request::route()->getAction()['title'])); ?></span>
                            </a>
                        <?php endif; ?>
                    </div>
                </div>

                
                <ul class="nav navbar-nav resp-wrap-icon <?php if(app()->getLocale() == 'ar'): ?> order-1 <?php else: ?> float-right <?php endif; ?>">
                    <li class="nav-item d-none d-lg-block">
                        <a class="nav-link" href="#" id="languageSelector">
                            <i class="ficon feather icon-globe"></i>
                            <span class="current-language"><?php echo e(app()->getLocale() == 'ar' ? 'الانجليزيه' : 'العربيه'); ?></span>
                        </a>
                    </li>
                    <li class="nav-item d-none d-lg-block">
                        <a class="nav-link" id="layout-mode"><i class="ficon feather icon-moon" onclick="changeMode()"></i></a>
                    </li>

                    <li class="nav-item d-none d-lg-block"><a class="nav-link nav-link-expand"><i class="ficon feather icon-maximize"></i></a></li>

                    
                    <li class="dropdown dropdown-notification nav-item d-none d-lg-block">
                        <a class="nav-link nav-link-label position-relative" href="#" data-toggle="dropdown">
                            <i class="ficon feather icon-bell"></i>
                            <span class="badge badge-pill badge-primary badge-up notification-count" id="notification-count" style="display: none;">0</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-media dropdown-menu-right">
                            <li class="dropdown-menu-header">
                                <div class="dropdown-header d-flex">
                                    <h4 class="notification-title mb-0 mr-auto"><?php echo e(__('provider.notifications')); ?></h4>
                                    <div class="badge badge-pill badge-light-primary notification-count-header">0 <?php echo e(__('provider.new')); ?></div>
                                </div>
                            </li>
                            <li class="scrollable-container media-list" id="notifications-list">
                                <div class="text-center p-2">
                                    <i class="feather icon-loader spin"></i> <?php echo e(__('provider.loading')); ?>...
                                </div>
                            </li>
                            <li class="dropdown-menu-footer">
                                <div class="d-flex justify-content-between">
                                    <a class="btn btn-sm btn-outline-primary" href="<?php echo e(route('provider.notifications.index')); ?>"><?php echo e(__('provider.view_all')); ?></a>
                                    <a class="btn btn-sm btn-primary" href="#" id="mark-all-read"><?php echo e(__('provider.mark_all_read')); ?></a>
                                </div>
                            </li>
                        </ul>
                    </li>
                    <li class="dropdown dropdown-user nav-item"><a class="dropdown-toggle nav-link dropdown-user-link" href="#" data-toggle="dropdown">
                            <div class="user-nav d-sm-flex d-none"><span class="user-name text-bold-600"><?php echo e(auth('provider')->user()->name); ?>

                         
                            </span><span class="user-status"><?php echo e(__('provider.available')); ?></span></div><span><img class="round" src="<?php echo e(auth('provider')->user()->avatar); ?>" alt="avatar" height="40" width="40"></span>
                        </a>
                        <div class="dropdown-menu dropdown-menu-right">
                            <a class="dropdown-item" href="<?php echo e(url('provider/profile')); ?>"><i class="feather icon-settings"></i> <?php echo e(__('site.profile')); ?></a>
                            <a class="dropdown-item" href="<?php echo e(url('provider/logout')); ?>"><i class="feather icon-power"></i> <?php echo e(__('site.logout')); ?></a>
                        </div>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</nav><?php /**PATH D:\Workstation\Taswk\elhafla\resources\views/provider/layout/partial/navbar.blade.php ENDPATH**/ ?>